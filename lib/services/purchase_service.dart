import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import '../services/base_auth_service.dart';
import '../services/api_auth_service.dart';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';
import 'api_service.dart';

// 会员级别枚举
enum MembershipTier { free, monthly, annual, lifetime }

// 会员产品枚举
enum MembershipProduct {
  monthlySubscription, // 月度订阅
  annualSubscription, // 年度订阅
  lifetimePurchase, // 永久会员购买
}

// 运行模式
enum RunMode {
  production, // 生产模式，使用真实支付
  sandbox, // 沙盒模式，使用测试账号支付
}

class PurchaseService extends ChangeNotifier {
  static final PurchaseService _instance = PurchaseService._internal();
  factory PurchaseService() => _instance;

  // 全局上下文，用于获取其他服务
  BuildContext? _context;

  // 设置上下文
  void setContext(BuildContext context) {
    _context = context;
  }

  // 定义产品ID（需要与Google Play Console和App Store Connect中设置的ID匹配）
  static const String monthlySubscriptionId = 'costtrack_month_sub';
  static const String annualSubscriptionId = 'costtrack_year_sub';
  static const String lifetimeMembershipId = 'costtract_lifetime';

  // 所有产品ID列表
  static const List<String> _productIds = [
    monthlySubscriptionId,
    annualSubscriptionId,
    lifetimeMembershipId,
  ];

  late final InAppPurchase _inAppPurchase;
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  List<ProductDetails> _products = [];
  List<PurchaseDetails> _purchases = [];

  // 状态变量
  bool _isAvailable = false;
  bool _isPurchasePending = false;
  bool _purchaseError = false;
  String? _purchaseErrorMessage;
  final List<String> _notFoundIds = [];

  // 存储会员选项信息
  Map<String, dynamic>? _membershipOptions;

  // 存储后端创建的订单ID
  String? _backendOrderId;

  // 添加重试机制变量
  static const int _maxRetries = 3;
  int _retryCount = 0;

  Timer? _retryTimer;

  // 添加运行模式
  RunMode _runMode = RunMode.production;
  RunMode get runMode => _runMode;

  // 获取所有产品
  List<ProductDetails> get products => _products;

  // 获取特定产品
  ProductDetails? getProduct(String id) {
    try {
      return _products.firstWhere((product) => product.id == id);
    } catch (e) {
      return null;
    }
  }

  // 各种状态获取器
  bool get isAvailable => _isAvailable;
  bool get isPurchasePending => _isPurchasePending;
  bool get purchaseError => _purchaseError;
  String? get purchaseErrorMessage => _purchaseErrorMessage;
  List<String> get notFoundIds => _notFoundIds;

  PurchaseService._internal() {
    developer.log('PurchaseService初始化开始', name: 'PurchaseService');
    _inAppPurchase = InAppPurchase.instance;

    developer.log('设置购买更新监听器', name: 'PurchaseService');
    final purchaseUpdated = _inAppPurchase.purchaseStream;
    _subscription = purchaseUpdated.listen(
      _onPurchaseUpdate,
      onDone: _updateStreamOnDone,
      onError: _updateStreamOnError,
    );

    // 确保 Android 特定设置
    if (Platform.isAndroid) {
      _configureAndroidBilling();
    }

    developer.log('PurchaseService初始化完成', name: 'PurchaseService');
  }

  // 配置 Android 计费客户端
  Future<void> _configureAndroidBilling() async {
    try {
      // 获取应用包信息，确保应用与 Play 商店匹配
      final packageInfo = await PackageInfo.fromPlatform();
      developer.log(
        'Android应用信息: ${packageInfo.packageName}, 版本: ${packageInfo.version}',
        name: 'PurchaseService',
      );

      // 针对 Android 的特殊配置
      if (Platform.isAndroid) {
        // 在 Android 上启用未经验证的购买
        final InAppPurchaseAndroidPlatformAddition androidAddition =
            InAppPurchase.instance
                .getPlatformAddition<InAppPurchaseAndroidPlatformAddition>();

        // 启用待处理购买 - 这是静态方法，不需要返回值
        InAppPurchaseAndroidPlatformAddition.enablePendingPurchases();
        developer.log('Android待处理购买已启用', name: 'PurchaseService');
      }
    } catch (e) {
      developer.log('配置Android计费时出错: $e', name: 'PurchaseService', error: e);
    }
  }

  // 初始化购买系统
  Future<void> initialize() async {
    developer.log('开始初始化应用内购买系统', name: 'PurchaseService');

    await _initializeWithRetry();
  }

  // 添加带重试机制的初始化方法
  Future<void> _initializeWithRetry() async {
    try {
      // 尝试检查购买服务是否可用
      _isAvailable = await _inAppPurchase.isAvailable();
      developer.log('购买服务可用性: $_isAvailable', name: 'PurchaseService');

      if (!_isAvailable) {
        if (_retryCount < _maxRetries) {
          // 如果不可用且未超过最大重试次数，等待后重试
          _retryCount++;
          final retryDelay = Duration(seconds: 2 * _retryCount); // 指数退避

          developer.log(
            '应用内购买不可用，将在${retryDelay.inSeconds}秒后重试 (尝试 $_retryCount/$_maxRetries)',
            name: 'PurchaseService',
          );

          // 取消之前的定时器（如果有）
          _retryTimer?.cancel();

          // 设置定时器进行重试
          _retryTimer = Timer(retryDelay, () async {
            await _initializeWithRetry();
          });
          return;
        } else {
          // 超过最大重试次数，放弃并设置错误状态
          developer.log(
            '在 $_maxRetries 次尝试后应用内购买仍不可用',
            name: 'PurchaseService',
          );
          _products = [];
          _purchases = [];
          _notFoundIds.clear();
          _purchaseError = true;
          _isPurchasePending = false;
          _purchaseErrorMessage = "应用内购买服务不可用";
          notifyListeners();
          return;
        }
      }

      // 重置重试计数，购买服务可用
      _retryCount = 0;

      // 在iOS上配置应用内购买
      if (Platform.isIOS) {
        await _configureIOSPurchase();
      }

      developer.log('查询可用产品信息', name: 'PurchaseService');
      await loadProducts();
    } catch (e) {
      developer.log('初始化购买服务时出错: $e', name: 'PurchaseService', error: e);

      if (_retryCount < _maxRetries) {
        // 如果出错且未超过最大重试次数，等待后重试
        _retryCount++;
        final retryDelay = Duration(seconds: 2 * _retryCount); // 指数退避

        developer.log(
          '初始化失败，将在${retryDelay.inSeconds}秒后重试 (尝试 $_retryCount/$_maxRetries)',
          name: 'PurchaseService',
        );

        // 取消之前的定时器（如果有）
        _retryTimer?.cancel();

        // 设置定时器进行重试
        _retryTimer = Timer(retryDelay, () async {
          await _initializeWithRetry();
        });
      } else {
        // 超过最大重试次数，放弃并设置错误状态
        _products = [];
        _purchaseError = true;
        _purchaseErrorMessage = "初始化应用内购买时出错: $e";
        notifyListeners();
      }
    }
  }

  // 在iOS上配置应用内购买
  Future<void> _configureIOSPurchase() async {
    if (!Platform.isIOS) {
      developer.log('非iOS平台，跳过iOS特定配置', name: 'PurchaseService');
      return;
    }

    try {
      var iosPlatformAddition =
          _inAppPurchase
              .getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();

      // 添加事务监听器
      await iosPlatformAddition.setDelegate(null);
      developer.log('iOS交易监听器已设置', name: 'PurchaseService');
    } catch (e) {
      developer.log('配置iOS购买时出错: $e', name: 'PurchaseService', error: e);
    }
  }

  // 请求应用商店审核
  Future<void> requestAppStoreReview() async {
    if (!Platform.isIOS) return;

    try {
      developer.log('iOS不支持直接请求应用商店审核，需要使用StoreKit', name: 'PurchaseService');
    } catch (e) {
      developer.log('请求应用商店审核时出错: $e', name: 'PurchaseService', error: e);
    }
  }

  // 在Android上使用产品
  Future<void> _checkAndUseAndroidProducts() async {
    if (!Platform.isAndroid) return;

    developer.log('检查Android产品列表', name: 'PurchaseService');

    try {
      // 此处仅记录产品列表，不进行类型转换
      // 之前的错误是尝试将List<ProductDetails>转换为Iterable<GooglePlayProductDetails>
      developer.log(
        'Android产品数量: ${_products.length}',
        name: 'PurchaseService',
      );

      // 正确的做法是，如果需要访问Google Play特定属性，单独处理每个产品
      for (final product in _products) {
        developer.log('处理产品: ${product.id}', name: 'PurchaseService');
        // 不要尝试将整个列表转换为GooglePlayProductDetails
      }
    } catch (e) {
      developer.log('检查Android产品时出错: $e', name: 'PurchaseService', error: e);
    }
  }

  // 加载产品信息
  Future<void> loadProducts() async {
    developer.log('开始加载产品信息: $_productIds', name: 'PurchaseService');
    try {
      final ProductDetailsResponse response = await _inAppPurchase
          .queryProductDetails(_productIds.toSet());

      developer.log(
        '产品查询响应: 找到=${response.productDetails.length}, 未找到=${response.notFoundIDs.length}',
        name: 'PurchaseService',
      );

      if (response.error != null) {
        developer.log(
          '查询产品时出错: ${response.error}',
          name: 'PurchaseService',
          error: response.error,
        );
        _purchaseError = true;
        _purchaseErrorMessage = "无法加载产品信息: ${response.error}";
      }

      // 直接赋值产品列表，不进行类型转换
      _products = response.productDetails;
      _notFoundIds.clear();
      _notFoundIds.addAll(response.notFoundIDs);

      // 打印找到的产品信息
      for (final product in _products) {
        developer.log(
          '找到产品: ${product.id}, 标题: ${product.title}, 描述: ${product.description}, 价格: ${product.price}',
          name: 'PurchaseService',
        );
      }

      // 打印未找到的产品ID
      for (final id in _notFoundIds) {
        developer.log('未找到产品ID: $id', name: 'PurchaseService');
      }

      // 如果在Android上，检查Android特定产品
      if (Platform.isAndroid) {
        await _checkAndUseAndroidProducts();
      }
    } catch (e) {
      developer.log('加载产品时发生异常: $e', name: 'PurchaseService', error: e);
      _purchaseError = true;
      _purchaseErrorMessage = "加载产品时出错: $e";
    } finally {
      notifyListeners();
    }
  }

  // 购买产品
  Future<bool> purchaseProduct(ProductDetails product) async {
    developer.log('开始购买产品: ${product.id}', name: 'PurchaseService');
    try {
      _isPurchasePending = true;
      _purchaseError = false;
      _purchaseErrorMessage = null;
      notifyListeners();

      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: product,
        applicationUserName: null, // 使用匿名购买模式
      );

      bool success = false;

      // 根据产品类型执行不同的购买流程
      if (product.id == lifetimeMembershipId) {
        // 一次性购买
        developer.log('执行一次性永久会员购买流程', name: 'PurchaseService');
        success = await _inAppPurchase.buyNonConsumable(
          purchaseParam: purchaseParam,
        );
      } else if (product.id == monthlySubscriptionId ||
          product.id == annualSubscriptionId) {
        // 订阅购买
        developer.log('执行订阅会员购买流程', name: 'PurchaseService');
        success = await _inAppPurchase.buyNonConsumable(
          purchaseParam: purchaseParam,
        );
      } else {
        developer.log('未知的产品类型: ${product.id}', name: 'PurchaseService');
        _purchaseError = true;
        _purchaseErrorMessage = "未知的产品类型";
        _isPurchasePending = false;
        notifyListeners();
        return false;
      }

      developer.log('购买启动结果: $success', name: 'PurchaseService');
      return success;
    } catch (e) {
      developer.log('购买过程中出现异常: $e', name: 'PurchaseService', error: e);
      _purchaseError = true;
      _purchaseErrorMessage = "购买过程中出错: $e";
      _isPurchasePending = false;
      notifyListeners();
      return false;
    }
  }

  // 购买月度会员
  Future<bool> purchaseMonthlySubscription() async {
    final product = getProduct(monthlySubscriptionId);
    if (product == null) {
      developer.log('月度会员产品不可用', name: 'PurchaseService');
      _purchaseError = true;
      _purchaseErrorMessage = "月度会员产品不可用";
      notifyListeners();
      return false;
    }
    return purchaseProduct(product);
  }

  // 购买年度会员
  Future<bool> purchaseAnnualSubscription() async {
    final product = getProduct(annualSubscriptionId);
    if (product == null) {
      developer.log('年度会员产品不可用', name: 'PurchaseService');
      _purchaseError = true;
      _purchaseErrorMessage = "年度会员产品不可用";
      notifyListeners();
      return false;
    }
    return purchaseProduct(product);
  }

  // 购买永久会员
  Future<bool> purchaseLifetimeMembership() async {
    final product = getProduct(lifetimeMembershipId);
    if (product == null) {
      developer.log('永久会员产品不可用', name: 'PurchaseService');
      _purchaseError = true;
      _purchaseErrorMessage = "永久会员产品不可用";
      notifyListeners();
      return false;
    }
    return purchaseProduct(product);
  }

  // 开发者工具：强制切换运行模式
  Future<void> setRunMode(RunMode mode) async {
    developer.log(
      '手动切换运行模式: ${_runMode.toString()} -> ${mode.toString()}',
      name: 'PurchaseService',
    );

    // 如果模式相同，不做处理
    if (_runMode == mode) {
      developer.log('模式相同，无需切换', name: 'PurchaseService');
      return;
    }

    // 保存新模式
    _runMode = mode;
    developer.log(
      '已切换到${mode == RunMode.production ? "生产" : "沙盒"}模式',
      name: 'PurchaseService',
    );

    // 重新初始化
    _products.clear();
    _retryCount = 0;
    await _initializeWithRetry();

    notifyListeners();
  }

  // 开发者工具：获取错误诊断信息
  String getDiagnosticInfo() {
    final buffer = StringBuffer();
    buffer.writeln('===== 结算服务诊断信息 =====');
    buffer.writeln('运行模式: $_runMode');
    buffer.writeln('结算服务可用: $_isAvailable');
    buffer.writeln('购买进行中: $_isPurchasePending');
    buffer.writeln('存在错误: $_purchaseError');
    buffer.writeln('错误信息: $_purchaseErrorMessage');
    buffer.writeln('已加载产品数: ${_products.length}');
    buffer.writeln('未找到产品ID: $_notFoundIds');
    buffer.writeln('重试次数: $_retryCount');

    if (_products.isNotEmpty) {
      buffer.writeln('\n可用产品:');
      for (final product in _products) {
        buffer.writeln('- ${product.id}: ${product.title} (${product.price})');
      }
    }

    return buffer.toString();
  }

  // 恢复购买
  Future<void> restorePurchases() async {
    developer.log('开始恢复购买', name: 'PurchaseService');

    try {
      _isPurchasePending = true;
      _purchaseError = false;
      _purchaseErrorMessage = null;
      notifyListeners();

      await _inAppPurchase.restorePurchases();
      // 具体的恢复结果将通过purchaseStream异步处理
    } catch (e) {
      developer.log('恢复购买时发生异常: $e', name: 'PurchaseService', error: e);
      _purchaseError = true;
      _purchaseErrorMessage = "恢复购买时出错: $e";
      _isPurchasePending = false;
      notifyListeners();
    }
  }

  // 购买更新处理
  Future<void> _onPurchaseUpdate(
    List<PurchaseDetails> purchaseDetailsList,
  ) async {
    _isPurchasePending = false;

    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      developer.log(
        '收到购买更新: 产品=${purchaseDetails.productID}, 状态=${purchaseDetails.status}',
        name: 'PurchaseService',
      );

      if (purchaseDetails.status == PurchaseStatus.pending) {
        developer.log('购买状态: 处理中', name: 'PurchaseService');
        _isPurchasePending = true;
      } else if (purchaseDetails.status == PurchaseStatus.purchased ||
          purchaseDetails.status == PurchaseStatus.restored) {
        bool isRestored = purchaseDetails.status == PurchaseStatus.restored;
        developer.log(
          '购买状态: ${isRestored ? "已恢复" : "已购买"}',
          name: 'PurchaseService',
        );

        // 提取订单ID和购买令牌
        String? orderId;
        String? purchaseToken;
        String? packageName;

        if (Platform.isAndroid) {
          // 对于Android，从GooglePlayPurchaseDetails中提取
          final GooglePlayPurchaseDetails? googlePlayPurchaseDetails =
              purchaseDetails as GooglePlayPurchaseDetails?;

          if (googlePlayPurchaseDetails != null) {
            // 从billingClientPurchase中获取必要信息
            final billingPurchase =
                googlePlayPurchaseDetails.billingClientPurchase;
            orderId = billingPurchase.orderId;
            purchaseToken = billingPurchase.purchaseToken;

            // 使用应用实际的包名，而不是从验证数据中提取
            packageName = "com.sato.calculate_cost_flutter";

            developer.log(
              '获取到Google Play购买信息: 订单ID=$orderId, 令牌=${purchaseToken.substring(0, 10)}..., 包名=$packageName',
              name: 'PurchaseService',
            );
          }
        } else if (Platform.isIOS) {
          // 对于iOS，从AppStorePurchaseDetails中提取
          final AppStorePurchaseDetails? appStorePurchaseDetails =
              purchaseDetails as AppStorePurchaseDetails?;

          if (appStorePurchaseDetails != null) {
            orderId =
                appStorePurchaseDetails
                    .skPaymentTransaction
                    .transactionIdentifier;
            purchaseToken =
                appStorePurchaseDetails.verificationData.serverVerificationData;

            developer.log(
              '获取到App Store购买信息: 交易ID=$orderId, 验证数据=${purchaseToken.substring(0, 10)}...',
              name: 'PurchaseService',
            );
          }
        }

        // 验证购买
        if (orderId != null && purchaseToken != null) {
          await _validatePurchase(
            purchaseDetails.productID,
            purchaseToken,
            orderId,
            packageName: packageName,
            isRestored: isRestored,
          );
        } else {
          developer.log('无法获取订单信息，跳过验证', name: 'PurchaseService');
          // 仍然完成交易，防止用户重复计费
          if (purchaseDetails.pendingCompletePurchase) {
            await InAppPurchase.instance.completePurchase(purchaseDetails);
          }
        }
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        developer.log(
          '购买错误: ${purchaseDetails.error?.message ?? "未知错误"}',
          name: 'PurchaseService',
        );
        _purchaseError = true;
        _purchaseErrorMessage = purchaseDetails.error?.message ?? "购买时发生错误";
      } else if (purchaseDetails.status == PurchaseStatus.canceled) {
        developer.log('购买已取消', name: 'PurchaseService');
      }

      notifyListeners();
    }
  }

  // 验证购买
  Future<void> _validatePurchase(
    String productId,
    String purchaseToken,
    String orderId, {
    String? packageName,
    bool isRestored = false,
  }) async {
    // 优先使用后端生成的订单ID
    String finalOrderId = _backendOrderId ?? orderId;

    developer.log(
      '开始验证购买: 产品=$productId, Google订单=$orderId, 后端订单ID=$finalOrderId${isRestored ? ", 恢复购买" : ""}',
      name: 'PurchaseService',
    );

    try {
      final ApiService apiService = ApiService();
      final validationResponse = await apiService.validatePurchase(
        productId,
        purchaseToken,
        finalOrderId,
        packageName: packageName,
      );

      // 清除后端订单ID，防止重复使用
      setBackendOrderId(null);

      if (validationResponse.isSuccess && validationResponse.data != null) {
        final validationData = validationResponse.data!;

        developer.log(
          '购买验证成功: 有效=${validationData.isActive}, 类型=${validationData.type}, '
          '到期日期=${validationData.expiryDate}, 自动续费=${validationData.autoRenewing}, '
          '一次性=${validationData.isOneTime}',
          name: 'PurchaseService',
        );

        // 处理订阅信息
        if (validationData.isActive) {
          await _processMembershipActivation(
            validationData.type,
            validationData.expiryDate,
            validationData.isOneTime,
          );
        } else {
          developer.log('购买验证结果显示未激活', name: 'PurchaseService');
        }
      } else {
        developer.log(
          '购买验证失败: ${validationResponse.error}',
          name: 'PurchaseService',
        );
        _purchaseError = true;
        _purchaseErrorMessage = "购买验证失败: ${validationResponse.error}";
      }
    } catch (e) {
      developer.log('验证购买过程中出错: $e', name: 'PurchaseService', error: e);
      _purchaseError = true;
      _purchaseErrorMessage = "验证购买过程中出错: $e";
    }

    // 无论验证结果如何，都要完成交易，防止重复计费
    PurchaseDetails? purchase;
    try {
      purchase = _purchases.firstWhere(
        (p) => p.productID == productId && p.pendingCompletePurchase,
      );
    } catch (e) {
      // 未找到匹配的购买，这是正常的
      purchase = null;
    }

    if (purchase != null && purchase.pendingCompletePurchase) {
      developer.log('完成交易: ${purchase.productID}', name: 'PurchaseService');
      await InAppPurchase.instance.completePurchase(purchase);
    }
  }

  // 处理会员激活
  Future<void> _processMembershipActivation(
    String type,
    String expiryDateStr,
    bool isOneTime,
  ) async {
    developer.log(
      '处理会员激活: 类型=$type, 到期日期=$expiryDateStr, 一次性=$isOneTime',
      name: 'PurchaseService',
    );

    try {
      // 解析到期日期
      DateTime? expiryDate;
      if (expiryDateStr.isNotEmpty) {
        try {
          expiryDate = DateTime.parse(expiryDateStr);
        } catch (e) {
          developer.log('解析到期日期失败: $e', name: 'PurchaseService');
        }
      }

      // 使用从验证服务器返回的信息和之前存储的会员选项
      bool finalIsOneTime = isOneTime;
      Duration? duration;

      // 如果有存储的会员选项，优先使用
      if (_membershipOptions != null) {
        developer.log(
          '使用之前保存的会员选项: $_membershipOptions',
          name: 'PurchaseService',
        );
        finalIsOneTime = _membershipOptions!['isOneTime'] as bool;

        // 恢复保存的时长
        if (_membershipOptions!.containsKey('duration') &&
            _membershipOptions!['duration'] != null) {
          duration = _membershipOptions!['duration'] as Duration;

          // 如果服务器未返回到期日期，我们使用本地计算的
          if (expiryDate == null && !finalIsOneTime) {
            expiryDate = DateTime.now().add(duration);
            developer.log('使用本地计算的到期日期: $expiryDate', name: 'PurchaseService');
          }
        }

        // 清除已使用的会员选项
        _membershipOptions = null;
      }

      // 使用最终确定的会员信息
      developer.log(
        '最终会员信息: 一次性=$finalIsOneTime, 到期日期=$expiryDate',
        name: 'PurchaseService',
      );

      // 如果上下文可用，更新用户会员状态
      if (_context != null) {
        final authService = Provider.of<BaseAuthService>(
          _context!,
          listen: false,
        );

        final success = await authService.upgradeToPremium(
          finalIsOneTime,
          expiryDate: expiryDate,
        );

        // 如果是ApiAuthService，则刷新用户资料
        if (authService is ApiAuthService) {
          developer.log('刷新会员资料', name: 'PurchaseService');
          await authService.refreshUserProfile();
        }

        // 显示成功消息
        if (success && _context != null && _context!.mounted) {
          developer.log('会员升级成功，显示通知', name: 'PurchaseService');
          ScaffoldMessenger.of(_context!).showSnackBar(
            const SnackBar(
              content: Text('恭喜您升级成功，感谢您的支持！'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }

        developer.log(
          '更新用户会员状态: ${success ? "成功" : "失败"}',
          name: 'PurchaseService',
        );
      } else {
        developer.log('无法更新用户会员状态：上下文不可用', name: 'PurchaseService');
      }
    } catch (e) {
      developer.log('处理会员激活时出错: $e', name: 'PurchaseService', error: e);
    }
  }

  // 流结束处理
  void _updateStreamOnDone() {
    developer.log('购买流监听已结束', name: 'PurchaseService');
    _subscription.cancel();
  }

  // 流错误处理
  void _updateStreamOnError(dynamic error) {
    developer.log('购买流监听发生错误: $error', name: 'PurchaseService', error: error);
  }

  // 服务销毁时取消订阅
  @override
  void dispose() {
    developer.log('PurchaseService即将销毁', name: 'PurchaseService');
    _subscription.cancel();
    _retryTimer?.cancel();
    super.dispose();
  }

  // 设置会员选项，在购买验证后进行处理
  void setMembershipOptions(Map<String, dynamic> options) {
    _membershipOptions = options;
    developer.log('已保存会员选项: $options', name: 'PurchaseService');
  }

  // 设置后端订单ID
  void setBackendOrderId(String? orderId) {
    _backendOrderId = orderId;
    if (orderId != null) {
      developer.log('已设置后端订单ID: $orderId', name: 'PurchaseService');
    } else {
      developer.log('清除后端订单ID', name: 'PurchaseService');
    }
  }
}
