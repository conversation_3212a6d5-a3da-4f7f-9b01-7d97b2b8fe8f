import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http_parser/http_parser.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:developer' as developer;
import '../models/api_models.dart';
import '../models/user.dart';
import '../models/item.dart';
import '../models/feedback.dart';
import '../utils/app_config.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;

  // 添加简单的缓存机制，避免频繁请求
  final Map<String, dynamic> _cache = {};
  final Map<String, DateTime> _cacheExpiry = {};
  // 默认缓存时间5分钟
  final Duration _defaultCacheTime = const Duration(minutes: 5);

  ApiService._internal() {
    // 使用AppConfig获取基础URL
    _baseUrl = AppConfig().apiBaseUrl;

    _dio = Dio(
      BaseOptions(
        baseUrl: _baseUrl,
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 30),
        sendTimeout: const Duration(seconds: 10),
        contentType: Headers.jsonContentType,
      ),
    );

    // 请求拦截器，添加token
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          developer.log(
            '发送请求: ${options.method} ${options.path}',
            name: 'ApiService.Interceptor',
          );
          developer.log(
            '请求Headers: ${options.headers}',
            name: 'ApiService.Interceptor',
          );

          if (options.data != null) {
            developer.log(
              '请求数据: ${options.data.toString()}',
              name: 'ApiService.Interceptor',
            );
          }

          final prefs = await SharedPreferences.getInstance();
          final token = prefs.getString('api_token');

          if (token != null &&
              options.path != '/api/v1/users/login' &&
              options.path != '/api/v1/users/register' &&
              options.path != '/api/v1/users/request-verification') {
            options.headers['Authorization'] = 'Bearer $token';
            developer.log(
              '添加令牌: Bearer ${token.substring(0, 10)}...',
              name: 'ApiService.Interceptor',
            );
          }
          return handler.next(options);
        },
        onResponse: (response, handler) {
          developer.log(
            '收到响应: ${response.statusCode} ${response.requestOptions.path}',
            name: 'ApiService.Interceptor',
          );
          developer.log(
            '响应数据: ${response.data.toString().substring(0, response.data.toString().length > 100 ? 100 : response.data.toString().length)}...',
            name: 'ApiService.Interceptor',
          );
          return handler.next(response);
        },
        onError: (DioException e, handler) async {
          developer.log(
            'API错误: ${e.message}',
            name: 'ApiService.Interceptor',
            error: e,
          );

          // 特殊处理401错误（未授权/令牌失效）
          if (e.response?.statusCode == 401) {
            developer.log('收到401未授权错误，自动清除令牌', name: 'ApiService.Interceptor');
            // 清除失效的令牌
            await clearToken();

            // 添加更详细的错误信息
            e = DioException(
              requestOptions: e.requestOptions,
              response: e.response,
              type: e.type,
              error: '用户会话已过期，请重新登录',
              message: '认证失败(401)：令牌已失效或已过期',
            );

            developer.log('已清除令牌并更新错误信息', name: 'ApiService.Interceptor');
          }

          return handler.next(e);
        },
      ),
    );
  }

  // API基本配置
  late final String _baseUrl;

  // 获取当前使用的API基础URL
  String get currentBaseUrl => _baseUrl;

  // 获取当前环境名称
  String get currentEnvironment => AppConfig().environmentName;

  late final Dio _dio;

  // 存储API token
  Future<void> _saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('api_token', token);
  }

  // 清除API token
  Future<void> clearToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('api_token');
  }

  // 请求验证码
  Future<ApiResponse<MessageResponse>> requestVerificationCode(
    String email,
    String purpose,
  ) async {
    try {
      final request = VerificationRequest(email: email, purpose: purpose);

      final response = await _dio.post(
        '/api/v1/users/request-verification',
        data: request.toJson(),
      );

      if (_isSuccessStatusCode(response.statusCode)) {
        developer.log(
          '验证码请求成功，状态码: ${response.statusCode}',
          name: 'ApiService',
        );
        return ApiResponse(
          data: MessageResponse(message: response.data['message']),
        );
      } else {
        developer.log(
          '验证码请求失败，状态码: ${response.statusCode}，错误: ${response.data['error']}',
          name: 'ApiService',
        );
        return ApiResponse(
          error: response.data['error'],
          code: response.data['code'],
        );
      }
    } on DioException catch (e) {
      developer.log('验证码请求异常', name: 'ApiService', error: e);
      return _handleDioError(e);
    }
  }

  // 用户注册
  Future<ApiResponse<User>> register(
    String email,
    String password,
    String displayName,
    String verifyCode,
  ) async {
    try {
      final request = RegisterRequest(
        email: email,
        password: password,
        displayName: displayName,
        verifyCode: verifyCode,
      );

      final response = await _dio.post(
        '/api/v1/users/register',
        data: request.toJson(),
      );

      if (_isSuccessStatusCode(response.statusCode)) {
        developer.log(
          '注册成功，状态码: ${response.statusCode}，用户数据: ${response.data}',
          name: 'ApiService',
        );
        return ApiResponse(data: User.fromApiJson(response.data));
      } else {
        developer.log(
          '注册失败，状态码: ${response.statusCode}，错误: ${response.data['error']}',
          name: 'ApiService',
        );
        return ApiResponse(
          error: response.data['error'],
          code: response.data['code'],
        );
      }
    } on DioException catch (e) {
      developer.log('注册请求异常', name: 'ApiService', error: e);
      return _handleDioError(e);
    }
  }

  // 检查网络连接
  Future<bool> isNetworkConnected() async {
    try {
      var connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      developer.log('检查网络连接失败: $e', name: 'ApiService');
      return false;
    }
  }

  // 用户登录
  Future<ApiResponse<LoginResponse>> login(
    String email,
    String password,
  ) async {
    developer.log('ApiService.login() 开始: $email', name: 'ApiService');

    // 先检查网络连接
    bool isConnected = await isNetworkConnected();
    if (!isConnected) {
      developer.log('网络连接不可用，无法登录', name: 'ApiService');
      return ApiResponse<LoginResponse>(error: '网络连接不可用，请检查网络设置');
    }

    try {
      final request = LoginRequest(email: email, password: password);
      developer.log('构建登录请求: ${request.toJson()}', name: 'ApiService');

      developer.log('发送登录请求到 $_baseUrl/api/v1/users/login', name: 'ApiService');
      final response = await _dio.post(
        '/api/v1/users/login',
        data: request.toJson(),
      );

      developer.log('收到登录响应: ${response.statusCode}', name: 'ApiService');
      developer.log('响应内容: ${response.data}', name: 'ApiService');

      if (_isSuccessStatusCode(response.statusCode)) {
        developer.log('登录成功，解析响应数据', name: 'ApiService');
        final loginResponse = LoginResponse.fromJson(response.data);

        // 保存token
        developer.log(
          '保存令牌: ${loginResponse.token.substring(0, 10)}...',
          name: 'ApiService',
        );
        await _saveToken(loginResponse.token);

        return ApiResponse(data: loginResponse);
      } else {
        developer.log('登录响应状态码异常: ${response.statusCode}', name: 'ApiService');
        return ApiResponse(
          error: response.data['error'],
          code: response.data['code'],
        );
      }
    } on DioException catch (e) {
      developer.log('登录请求异常', name: 'ApiService', error: e);
      if (e.response != null) {
        developer.log('错误响应: ${e.response!.data}', name: 'ApiService');
      }
      return _handleDioError(e);
    }
  }

  // 获取用户信息
  Future<ApiResponse<User>> getUserProfile() async {
    const cacheKey = 'user_profile';

    // 检查是否有有效的缓存
    if (_isCacheValid(cacheKey)) {
      developer.log('使用缓存的用户资料', name: 'ApiService');
      return _cache[cacheKey] as ApiResponse<User>;
    }

    try {
      developer.log('请求用户资料', name: 'ApiService');
      final response = await _dio.get('/api/v1/users/profile');

      final apiResponse = _createUserProfileResponse(response);

      // 缓存响应结果
      _cacheResponse(cacheKey, apiResponse);

      return apiResponse;
    } on DioException catch (e) {
      return _handleDioError(e);
    }
  }

  // 创建用户资料响应
  ApiResponse<User> _createUserProfileResponse(Response response) {
    if (_isSuccessStatusCode(response.statusCode)) {
      return ApiResponse(data: User.fromApiJson(response.data));
    } else {
      return ApiResponse(
        error: response.data['error'],
        code: response.data['code'],
      );
    }
  }

  // 检查缓存是否有效
  bool _isCacheValid(String key) {
    if (!_cache.containsKey(key) || !_cacheExpiry.containsKey(key)) {
      return false;
    }

    final expiry = _cacheExpiry[key]!;
    return DateTime.now().isBefore(expiry);
  }

  // 缓存响应
  void _cacheResponse(String key, dynamic response, {Duration? cacheDuration}) {
    _cache[key] = response;
    _cacheExpiry[key] = DateTime.now().add(cacheDuration ?? _defaultCacheTime);
    developer.log('响应已缓存，有效期至: ${_cacheExpiry[key]}', name: 'ApiService');
  }

  // 清除特定缓存
  void clearCache(String key) {
    _cache.remove(key);
    _cacheExpiry.remove(key);
  }

  // 清除所有缓存
  void clearAllCache() {
    _cache.clear();
    _cacheExpiry.clear();
  }

  // 获取物品列表
  Future<ApiResponse<List<Item>>> getUserItems(int userId) async {
    try {
      final response = await _dio.get('/api/v1/items/user/$userId');

      if (_isSuccessStatusCode(response.statusCode)) {
        final ItemsResponse itemsResponse = ItemsResponse.fromJson(
          response.data,
        );
        return ApiResponse(data: itemsResponse.items);
      } else {
        return ApiResponse(
          error: response.data['error'],
          code: response.data['code'],
        );
      }
    } on DioException catch (e) {
      return _handleDioError(e);
    }
  }

  // 获取物品详情
  Future<ApiResponse<Item>> getItemDetails(int itemId) async {
    try {
      final response = await _dio.get('/api/v1/items/$itemId');

      if (_isSuccessStatusCode(response.statusCode)) {
        final ItemResponse itemResponse = ItemResponse.fromJson(response.data);
        return ApiResponse(data: itemResponse.item);
      } else {
        return ApiResponse(
          error: response.data['error'],
          code: response.data['code'],
        );
      }
    } on DioException catch (e) {
      return _handleDioError(e);
    }
  }

  // 辅助函数：检查是否为成功状态码
  bool _isSuccessStatusCode(int? statusCode) {
    final code = statusCode ?? 0;
    return code >= 200 && code < 300;
  }

  // 添加物品
  Future<ApiResponse<Item>> addItem(Item item, {File? imageFile}) async {
    try {
      FormData formData;

      // 检查物品是否有本地图片路径
      File? imageToUpload = imageFile;
      if (imageToUpload == null &&
          item.imagePath != null &&
          item.imagePath!.isNotEmpty) {
        // 检查本地图片是否存在
        final file = File(item.imagePath!);
        if (await file.exists()) {
          try {
            final fileSize = await file.length();
            if (fileSize > 100) {
              // 简单检查文件是否有效
              imageToUpload = file;
              developer.log(
                '使用物品中的本地图片路径: ${item.imagePath}',
                name: 'ApiService',
              );
            }
          } catch (e) {
            developer.log('检查本地图片文件时出错: $e', name: 'ApiService');
          }
        } else {
          developer.log('物品图片文件不存在: ${item.imagePath}', name: 'ApiService');
        }
      }

      // 准备表单数据
      if (imageToUpload != null) {
        // 确定MIME类型
        final mimeType = _getMimeType(imageToUpload.path);

        // 创建图片表单项
        final imageFormData = await MultipartFile.fromFile(
          imageToUpload.path,
          filename: imageToUpload.path.split('/').last,
          contentType: mimeType,
        );

        // 创建带图片的表单
        formData = FormData.fromMap({
          'data': jsonEncode(item.toApiJson()),
          'image': imageFormData,
        });

        developer.log('上传物品带图片: ${item.name}', name: 'ApiService');
      } else {
        // 仅包含数据的表单
        formData = FormData.fromMap({'data': jsonEncode(item.toApiJson())});
        developer.log('上传物品不带图片: ${item.name}', name: 'ApiService');
      }

      final response = await _dio.post('/api/v1/items', data: formData);

      if (_isSuccessStatusCode(response.statusCode)) {
        final ItemResponse itemResponse = ItemResponse.fromJson(response.data);
        developer.log(
          '物品上传成功: ${item.name}, 返回ID: ${itemResponse.item.id}, 状态码: ${response.statusCode}',
          name: 'ApiService',
        );
        return ApiResponse(data: itemResponse.item);
      } else {
        developer.log('物品上传失败: ${response.data['error']}', name: 'ApiService');
        return ApiResponse(
          error: response.data['error'],
          code: response.data['code'],
        );
      }
    } on DioException catch (e) {
      developer.log('上传物品时发生DIO错误', name: 'ApiService', error: e);
      return _handleDioError(e);
    } catch (e) {
      developer.log('上传物品时发生未知错误: $e', name: 'ApiService');
      return ApiResponse(error: '上传物品时出错: $e');
    }
  }

  // 更新物品
  Future<ApiResponse<Item>> updateItem(Item item, {File? imageFile}) async {
    try {
      if (item.id == null) {
        return ApiResponse(error: '物品ID不能为空');
      }

      FormData formData;

      // 准备表单数据
      if (imageFile != null) {
        // 确定MIME类型
        final mimeType = _getMimeType(imageFile.path);

        // 创建图片表单项
        final imageFormData = await MultipartFile.fromFile(
          imageFile.path,
          filename: imageFile.path.split('/').last,
          contentType: mimeType,
        );

        // 创建带图片的表单
        formData = FormData.fromMap({
          'data': jsonEncode(item.toApiJson()),
          'image': imageFormData,
        });
      } else {
        // 仅包含数据的表单
        formData = FormData.fromMap({'data': jsonEncode(item.toApiJson())});
      }

      final response = await _dio.put(
        '/api/v1/items/${item.id}',
        data: formData,
      );

      if (_isSuccessStatusCode(response.statusCode)) {
        final ItemResponse itemResponse = ItemResponse.fromJson(response.data);
        developer.log(
          '物品更新成功: ${item.name}, ID: ${item.id}, 状态码: ${response.statusCode}',
          name: 'ApiService',
        );
        return ApiResponse(data: itemResponse.item);
      } else {
        return ApiResponse(
          error: response.data['error'],
          code: response.data['code'],
        );
      }
    } on DioException catch (e) {
      return _handleDioError(e);
    }
  }

  // 删除物品
  Future<ApiResponse<MessageResponse>> deleteItem(int itemId) async {
    try {
      final response = await _dio.delete('/api/v1/items/$itemId');

      if (_isSuccessStatusCode(response.statusCode)) {
        return ApiResponse(
          data: MessageResponse(message: response.data['message']),
        );
      } else {
        return ApiResponse(
          error: response.data['error'],
          code: response.data['code'],
        );
      }
    } on DioException catch (e) {
      return _handleDioError(e);
    }
  }

  // 提交反馈
  Future<ApiResponse<UserFeedback>> submitFeedback(
    UserFeedback feedback,
  ) async {
    try {
      final response = await _dio.post(
        '/api/v1/feedback',
        data: feedback.toApiJson(),
      );

      if (_isSuccessStatusCode(response.statusCode)) {
        return ApiResponse(data: UserFeedback.fromJson(response.data));
      } else {
        return ApiResponse(
          error: response.data['error'],
          code: response.data['code'],
        );
      }
    } on DioException catch (e) {
      return _handleDioError(e);
    }
  }

  // 备份物品数据
  Future<ApiResponse<MessageResponse>> backupItems(List<Item> items) async {
    try {
      developer.log('开始备份物品数据: ${items.length}个物品', name: 'ApiService');

      if (items.isEmpty) {
        return ApiResponse(data: MessageResponse(message: '没有物品需要备份'));
      }

      // 转换物品列表为API JSON格式
      final itemsJson = items.map((item) => item.toApiJson()).toList();

      // 发送备份请求到API
      final response = await _dio.post(
        '/api/v1/items/backup',
        data: {'items': itemsJson},
      );

      if (response.statusCode == 200) {
        developer.log('备份成功，已备份${items.length}个物品', name: 'ApiService');
        return ApiResponse(
          data: MessageResponse(
            message: response.data['message'] ?? '备份成功，共${items.length}个物品',
          ),
        );
      } else {
        developer.log('备份失败: ${response.data['error']}', name: 'ApiService');
        return ApiResponse(
          error: response.data['error'] ?? '备份请求失败',
          code: response.data['code'],
        );
      }
    } on DioException catch (e) {
      developer.log('备份过程中发生错误', name: 'ApiService', error: e);
      return _handleDioError(e);
    }
  }

  // 恢复物品数据
  Future<ApiResponse<List<Item>>> restoreItems() async {
    try {
      developer.log('开始恢复物品数据', name: 'ApiService');

      final response = await _dio.get('/api/v1/backup');

      if (_isSuccessStatusCode(response.statusCode) &&
          response.data['items'] != null) {
        final List<dynamic> rawItems = response.data['items'];
        final items = rawItems.map((json) => Item.fromApiJson(json)).toList();

        developer.log('恢复成功，获取到${items.length}个物品', name: 'ApiService');
        return ApiResponse(data: items);
      } else {
        developer.log('恢复失败: ${response.data['error']}', name: 'ApiService');
        return ApiResponse(
          error: response.data['error'] ?? '恢复失败',
          code: response.data['code'],
        );
      }
    } on DioException catch (e) {
      developer.log('恢复过程中发生错误', name: 'ApiService', error: e);
      return _handleDioError(e);
    }
  }

  // 批量删除物品
  Future<ApiResponse<Map<String, int>>> batchDeleteItems(
    List<int> itemIds,
  ) async {
    try {
      developer.log(
        '开始批量删除物品: ${itemIds.length}个物品, IDs: $itemIds',
        name: 'ApiService',
      );

      if (itemIds.isEmpty) {
        return ApiResponse(data: {'deleted_count': 0, 'failed_count': 0});
      }

      // 发送批量删除请求到API
      final response = await _dio.delete(
        '/api/v1/items/batch',
        data: {'ids': itemIds},
      );

      if (_isSuccessStatusCode(response.statusCode)) {
        developer.log(
          '批量删除成功: ${response.data}, 状态码: ${response.statusCode}',
          name: 'ApiService',
        );
        return ApiResponse(
          data: {
            'deleted_count': response.data['deleted_count'] ?? 0,
            'failed_count': response.data['failed_count'] ?? 0,
          },
        );
      } else {
        developer.log('批量删除失败: ${response.data['error']}', name: 'ApiService');
        return ApiResponse(
          error: response.data['error'] ?? '批量删除请求失败',
          code: response.data['code'],
        );
      }
    } on DioException catch (e) {
      developer.log('批量删除过程中发生错误', name: 'ApiService', error: e);
      return _handleDioError(e);
    }
  }

  // 重置密码
  Future<ApiResponse<MessageResponse>> resetPassword(
    String email,
    String verificationCode,
    String newPassword,
  ) async {
    developer.log(
      '开始重置密码API调用: email=$email, 验证码长度=${verificationCode.length}',
      name: 'ApiService',
    );

    try {
      final request = ResetPasswordRequest(
        email: email,
        verificationCode: verificationCode,
        newPassword: newPassword,
      );

      developer.log('发送重置密码请求: ${request.toJson()}', name: 'ApiService');

      final response = await _dio.post(
        '/api/v1/users/reset-password',
        data: request.toJson(),
      );

      developer.log('重置密码响应状态码: ${response.statusCode}', name: 'ApiService');

      if (_isSuccessStatusCode(response.statusCode)) {
        developer.log(
          '重置密码成功: ${response.data["message"]}',
          name: 'ApiService',
        );
        return ApiResponse(
          data: MessageResponse(message: response.data['message']),
        );
      } else {
        developer.log('重置密码失败: ${response.data["error"]}', name: 'ApiService');
        return ApiResponse(
          error: response.data['error'],
          code: response.data['code'],
        );
      }
    } on DioException catch (e) {
      developer.log('重置密码请求异常', name: 'ApiService', error: e);
      if (e.response != null) {
        developer.log('错误响应: ${e.response!.data}', name: 'ApiService');
      }
      return _handleDioError(e);
    } catch (e) {
      developer.log('重置密码未知异常: $e', name: 'ApiService', error: e);
      return ApiResponse(error: '重置密码时出错: $e');
    }
  }

  // 处理Dio错误
  ApiResponse<T> _handleDioError<T>(DioException e) {
    developer.log('处理API错误: ${e.type}', name: 'ApiService');

    String errorMessage;
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        errorMessage = '连接超时，请检查网络';
        break;
      case DioExceptionType.sendTimeout:
        errorMessage = '发送请求超时，请检查网络';
        break;
      case DioExceptionType.receiveTimeout:
        errorMessage = '服务器响应超时，请稍后重试。可能是网络问题或服务器处理时间过长';
        break;
      case DioExceptionType.badResponse:
        try {
          // 尝试从响应中提取错误信息
          final response = e.response;
          if (response != null && response.data != null) {
            // 特殊处理401错误
            if (response.statusCode == 401) {
              errorMessage = '登录会话已过期，请重新登录';
            } else if (response.data is Map && response.data['error'] != null) {
              errorMessage = response.data['error'];
            } else {
              errorMessage = '服务器返回错误: ${response.statusCode}';
            }
          } else {
            errorMessage = '未知服务器错误';
          }
        } catch (_) {
          errorMessage = '服务器响应异常';
        }
        break;
      case DioExceptionType.cancel:
        errorMessage = '请求被取消';
        break;
      default:
        if (e.error is SocketException) {
          errorMessage = '网络连接失败，请检查网络设置';
        } else {
          errorMessage = '发生未知错误: ${e.message}';
        }
    }

    developer.log('API错误信息: $errorMessage', name: 'ApiService');
    return ApiResponse<T>(error: errorMessage);
  }

  // 获取文件MIME类型
  MediaType? _getMimeType(String path) {
    final ext = path.split('.').last.toLowerCase();
    switch (ext) {
      case 'jpg':
      case 'jpeg':
        return MediaType('image', 'jpeg');
      case 'png':
        return MediaType('image', 'png');
      case 'gif':
        return MediaType('image', 'gif');
      case 'webp':
        return MediaType('image', 'webp');
      default:
        return MediaType('application', 'octet-stream');
    }
  }

  // 创建订阅订单
  Future<ApiResponse<OrderResponse>> createSubscriptionOrder(
    String productId,
    double amount,
    String description, {
    String currency = 'CNY',
  }) async {
    try {
      developer.log(
        '开始创建订阅订单: 产品=$productId, 金额=$amount, 货币=$currency',
        name: 'ApiService',
      );

      final request = CreateOrderRequest(
        productId: productId,
        amount: amount,
        currency: currency,
        description: description,
      );

      final response = await _dio.post(
        '/api/v1/subscriptions/create-order',
        data: request.toJson(),
      );

      if (_isSuccessStatusCode(response.statusCode)) {
        developer.log('订单创建成功', name: 'ApiService');
        return ApiResponse(data: OrderResponse.fromJson(response.data));
      } else {
        developer.log('订单创建失败: ${response.data['error']}', name: 'ApiService');
        return ApiResponse(
          error: response.data['error'] ?? '订单创建失败',
          code: response.data['code'],
        );
      }
    } on DioException catch (e) {
      developer.log('创建订单过程中发生错误', name: 'ApiService', error: e);
      return _handleDioError(e);
    } catch (e) {
      developer.log('创建订单过程中发生未知错误: $e', name: 'ApiService', error: e);
      return ApiResponse(error: '订单创建失败: $e');
    }
  }

  // 验证购买
  Future<ApiResponse<PurchaseValidationResponse>> validatePurchase(
    String productId,
    String purchaseToken,
    String orderId, {
    String? packageName,
  }) async {
    try {
      developer.log('开始验证购买: 产品=$productId, 订单ID=$orderId', name: 'ApiService');

      final request = PurchaseValidationRequest(
        productId: productId,
        purchaseToken: purchaseToken,
        orderId: orderId,
        packageName: packageName,
      );

      final response = await _dio.post(
        '/api/v1/subscriptions/validate-purchase',
        data: request.toJson(),
      );

      if (_isSuccessStatusCode(response.statusCode)) {
        developer.log('购买验证成功', name: 'ApiService');
        return ApiResponse(
          data: PurchaseValidationResponse.fromJson(response.data),
        );
      } else {
        developer.log('购买验证失败: ${response.data['error']}', name: 'ApiService');
        return ApiResponse(
          error: response.data['error'] ?? '购买验证失败',
          code: response.data['code'],
        );
      }
    } on DioException catch (e) {
      developer.log('验证购买过程中发生错误', name: 'ApiService', error: e);
      return _handleDioError(e);
    } catch (e) {
      developer.log('验证购买过程中发生未知错误: $e', name: 'ApiService', error: e);
      return ApiResponse(error: '购买验证失败: $e');
    }
  }
}
