import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:developer' as developer;
import '../models/user.dart';
import 'database_service.dart';
import 'verification_service.dart';
import 'base_auth_service.dart';

class AuthService extends BaseAuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final VerificationService _verificationService = VerificationService();

  User? _currentUser;
  @override
  User? get currentUser => _currentUser;

  // 免费用户的最大物品数量限制
  static const int freeUserItemLimit = 30;

  // 检查用户是否已登录
  @override
  bool get isLoggedIn => _currentUser != null;

  // 检查是否为高级会员
  @override
  bool get isPremium => _currentUser?.userType == UserType.premium;

  // 检查会员是否已过期
  @override
  bool get isMembershipExpired {
    if (_currentUser == null || _currentUser!.userType == UserType.free) {
      return true;
    }

    if (_currentUser!.membershipExpiryDate == null) {
      return false; // 如果是永久会员（一次性购买），则不会过期
    }

    // 检查过期日期
    return DateTime.now().isAfter(_currentUser!.membershipExpiryDate!);
  }

  // 检查是否可以添加更多物品
  @override
  Future<bool> canAddMoreItems() async {
    // 如果是高级会员且未过期，无限制
    if (isPremium && !isMembershipExpired) {
      return true;
    }

    // 免费用户或未登录用户都直接允许添加（最大30个）
    return true;
  }

  // 获取免费用户剩余可添加物品数量
  @override
  Future<int> getRemainingItemQuota() async {
    debugPrint('调试-Auth: 获取剩余物品配额');
    debugPrint(
      '调试-Auth: 当前用户: ${_currentUser?.displayName}, ID: ${_currentUser?.id}',
    );

    // 高级会员返回无限制
    if (isPremium && !isMembershipExpired) {
      debugPrint('调试-Auth: 用户是高级会员且未过期，无限制');
      return -1; // -1表示无限制
    }

    // 获取当前用户的物品数量
    int currentItemCount = 0;

    try {
      if (_currentUser != null && _currentUser!.id != null) {
        // 从数据库获取实际物品数量
        currentItemCount = await _databaseService.getUserItemCount(
          _currentUser!.id!,
        );
        debugPrint('调试-Auth: 当前用户物品数量: $currentItemCount');
      } else {
        // 用户未登录，获取存储在SharedPreferences中的userId
        final prefs = await SharedPreferences.getInstance();
        final userId = prefs.getInt('userId');

        if (userId != null) {
          // 有存储的用户ID，获取该用户的物品数量
          currentItemCount = await _databaseService.getUserItemCount(userId);
          debugPrint('调试-Auth: 未登录但有存储的用户ID: $userId, 物品数量: $currentItemCount');
        } else {
          debugPrint('调试-Auth: 用户完全未登录，允许使用全部免费配额');
          return freeUserItemLimit; // 完全未登录的用户允许使用全部免费配额
        }
      }
    } catch (e) {
      debugPrint('调试-Auth: 获取物品数量时出错: $e');
      return freeUserItemLimit; // 出错时也允许使用全部配额
    }

    // 计算剩余可添加物品数量
    final remaining = freeUserItemLimit - currentItemCount;
    debugPrint('调试-Auth: 剩余可添加物品配额: $remaining');

    return remaining > 0 ? remaining : 0; // 确保不返回负数
  }

  // 初始化，检查用户是否已登录
  @override
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final userId = prefs.getInt('userId');

    if (userId != null) {
      final user = await _databaseService.getUserById(userId);

      if (user != null) {
        _currentUser = user;
        notifyListeners();
      }
    }
  }

  // 发送注册验证码
  @override
  Future<bool> sendRegisterCode(String email) async {
    try {
      // 首先检查邮箱是否已被使用
      final existingUser = await _databaseService.getUserByEmail(email);
      if (existingUser != null) {
        return false; // 邮箱已被注册
      }

      // 发送验证码
      return await _verificationService.sendCode(email);
    } catch (e) {
      debugPrint('AuthService: 发送注册验证码失败: $e');
      return false;
    }
  }

  // 发送重置密码验证码
  @override
  Future<bool> sendResetPasswordCode(String email) async {
    try {
      // 首先检查邮箱是否存在
      final existingUser = await _databaseService.getUserByEmail(email);
      if (existingUser == null) {
        return false; // 邮箱不存在
      }

      // 发送验证码
      return await _verificationService.sendCode(email);
    } catch (e) {
      debugPrint('AuthService: 发送重置密码验证码失败: $e');
      return false;
    }
  }

  // 用户注册(带验证码验证)
  @override
  Future<bool> registerWithVerification(
    String email,
    String password,
    String displayName,
    String verificationCode,
  ) async {
    try {
      // 首先检查邮箱是否已被使用
      final existingUser = await _databaseService.getUserByEmail(email);
      if (existingUser != null) {
        return false; // 邮箱已被注册
      }

      // 验证验证码
      final isCodeValid = await _verificationService.verifyCode(
        email,
        verificationCode,
      );
      if (!isCodeValid) {
        return false; // 验证码无效或已过期
      }

      // 创建新用户
      final newUser = User(
        id: null,
        email: email,
        displayName: displayName,
        userType: UserType.free,
        registrationDate: DateTime.now(),
      );

      // 存储新用户信息
      final userId = await _databaseService.insertUser(newUser, password);

      // 清除验证码
      await _verificationService.clearCode(email);

      return userId > 0;
    } catch (e) {
      debugPrint('AuthService: 注册失败: $e');
      return false;
    }
  }

  // 通过验证码重置密码
  @override
  Future<bool> resetPasswordWithVerification(
    String email,
    String newPassword,
    String verificationCode,
  ) async {
    developer.log(
      '开始重置密码流程: email=$email, 验证码长度=${verificationCode.length}',
      name: 'AuthService',
    );

    try {
      // 首先检查邮箱是否存在
      developer.log('检查邮箱是否存在: $email', name: 'AuthService');
      final existingUser = await _databaseService.getUserByEmail(email);
      if (existingUser == null) {
        developer.log('重置密码失败: 邮箱不存在 - $email', name: 'AuthService');
        return false; // 邮箱不存在
      }
      developer.log(
        '邮箱存在，找到用户: ${existingUser.displayName}',
        name: 'AuthService',
      );

      // 验证验证码
      developer.log('开始验证验证码', name: 'AuthService');
      final isCodeValid = await _verificationService.verifyCode(
        email,
        verificationCode,
      );
      if (!isCodeValid) {
        developer.log('重置密码失败: 验证码无效或已过期', name: 'AuthService');
        return false; // 验证码无效或已过期
      }
      developer.log('验证码验证成功', name: 'AuthService');

      // 更新密码
      developer.log('开始更新用户密码', name: 'AuthService');
      final result = await _databaseService.updatePassword(
        existingUser.id!,
        newPassword,
      );

      developer.log('密码更新结果: $result', name: 'AuthService');

      // 清除验证码
      developer.log('清除已使用的验证码', name: 'AuthService');
      await _verificationService.clearCode(email);

      if (result > 0) {
        developer.log('重置密码成功完成', name: 'AuthService');
      } else {
        developer.log('重置密码失败: 数据库更新失败', name: 'AuthService');
      }

      return result > 0;
    } catch (e) {
      developer.log('重置密码过程中出现异常: $e', name: 'AuthService', error: e);
      return false;
    }
  }

  // 用户注册（原方法保留向后兼容）
  @override
  Future<bool> register(
    String email,
    String password,
    String displayName,
  ) async {
    try {
      // 首先检查邮箱是否已被使用
      final existingUser = await _databaseService.getUserByEmail(email);
      if (existingUser != null) {
        return false; // 邮箱已被注册
      }

      // 创建新用户
      final newUser = User(
        id: null,
        email: email,
        displayName: displayName,
        userType: UserType.free,
        registrationDate: DateTime.now(),
      );

      // 存储新用户信息
      final userId = await _databaseService.insertUser(newUser, password);

      if (userId > 0) {
        return true; // 注册成功
      } else {
        return false; // 注册失败
      }
    } catch (e) {
      debugPrint('AuthService: 注册失败: $e');
      return false;
    }
  }

  // 用户登录
  @override
  Future<bool> login(String email, String password) async {
    try {
      developer.log('AuthService.login() 开始: $email', name: 'AuthService');

      // 验证登录信息
      developer.log('尝试验证用户密码', name: 'AuthService');
      final userId = await _databaseService.validateUser(email, password);
      developer.log('验证结果 userId: $userId', name: 'AuthService');

      if (userId != null) {
        // 获取用户数据
        developer.log('尝试获取用户数据', name: 'AuthService');
        final user = await _databaseService.getUserById(userId);
        developer.log('获取到用户: ${user?.displayName}', name: 'AuthService');

        if (user != null) {
          // 存储登录状态
          developer.log('保存登录状态', name: 'AuthService');
          final prefs = await SharedPreferences.getInstance();
          await prefs.setInt('userId', userId);

          // 更新当前用户
          _currentUser = user;
          notifyListeners();
          developer.log('登录成功', name: 'AuthService');
          return true;
        }
      }

      developer.log('登录失败: 用户名或密码不正确', name: 'AuthService');
      return false;
    } catch (e) {
      developer.log('登录失败: $e', name: 'AuthService', error: e);
      return false;
    }
  }

  // 用户登出
  @override
  Future<void> logout() async {
    // 清除登录状态
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('userId');

    // 清除当前用户
    _currentUser = null;
    notifyListeners();
  }

  // 升级为高级会员
  @override
  Future<bool> upgradeToPremium(bool isOneTime, {DateTime? expiryDate}) async {
    if (_currentUser == null) {
      return false;
    }

    try {
      // 更新用户类型为高级会员
      final updatedUser = _currentUser!.copyWith(
        userType: UserType.premium,
        membershipExpiryDate: isOneTime ? null : expiryDate,
      );

      // 保存到数据库
      final result = await _databaseService.updateUser(updatedUser);

      if (result > 0) {
        // 更新当前用户
        _currentUser = updatedUser;
        notifyListeners();
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('AuthService: 升级会员失败: $e');
      return false;
    }
  }

  // 取消高级会员
  @override
  Future<bool> cancelPremium() async {
    if (_currentUser == null) {
      return false;
    }

    try {
      // 更新用户类型为免费用户
      final updatedUser = _currentUser!.copyWith(
        userType: UserType.free,
        membershipExpiryDate: null,
      );

      // 保存到数据库
      final result = await _databaseService.updateUser(updatedUser);

      if (result > 0) {
        // 更新当前用户
        _currentUser = updatedUser;
        notifyListeners();
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('AuthService: 取消会员失败: $e');
      return false;
    }
  }

  // 更改用户密码
  @override
  Future<bool> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    if (_currentUser == null) {
      return false;
    }

    try {
      // 验证当前密码是否正确
      final userId = await _databaseService.validateUser(
        _currentUser!.email,
        currentPassword,
      );

      if (userId == null) {
        return false; // 当前密码不正确
      }

      // 更新密码
      final result = await _databaseService.updatePassword(userId, newPassword);

      return result > 0;
    } catch (e) {
      debugPrint('AuthService: 更改密码失败: $e');
      return false;
    }
  }

  // 更新用户信息
  @override
  Future<bool> updateUserInfo(String displayName) async {
    if (_currentUser == null) {
      return false;
    }

    try {
      // 更新用户信息
      final updatedUser = _currentUser!.copyWith(displayName: displayName);

      // 保存到数据库
      final result = await _databaseService.updateUser(updatedUser);

      if (result > 0) {
        // 更新当前用户
        _currentUser = updatedUser;
        notifyListeners();
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('AuthService: 更新用户信息失败: $e');
      return false;
    }
  }

  // 刷新用户资料（本地存储版本）
  @override
  Future<bool> refreshUserProfile() async {
    if (_currentUser == null) {
      return false;
    }

    try {
      // 从数据库重新获取用户信息
      final user = await _databaseService.getUserById(_currentUser!.id!);
      if (user != null) {
        _currentUser = user;
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('AuthService: 刷新用户资料失败: $e');
      return false;
    }
  }

  // 强制刷新用户资料（本地存储版本）
  @override
  Future<bool> forceRefreshUserProfile() async {
    // 对于本地存储，强制刷新和普通刷新是一样的
    return await refreshUserProfile();
  }
}
