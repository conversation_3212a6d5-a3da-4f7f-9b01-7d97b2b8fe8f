import 'package:flutter/foundation.dart';
import '../models/user.dart';

/// 认证服务的基础接口，同时支持本地存储和API存储
abstract class BaseAuthService with ChangeNotifier {
  User? get currentUser;

  // 检查状态
  bool get isLoggedIn;
  bool get isPremium;
  bool get isMembershipExpired;

  // 功能方法
  Future<void> initialize();
  Future<bool> login(String email, String password);
  Future<void> logout();
  Future<bool> register(String email, String password, String displayName);
  Future<bool> canAddMoreItems();
  Future<int> getRemainingItemQuota();

  // 验证码相关方法
  Future<bool> sendRegisterCode(String email);
  Future<bool> sendResetPasswordCode(String email);
  Future<bool> registerWithVerification(
    String email,
    String password,
    String displayName,
    String verificationCode,
  );
  Future<bool> resetPasswordWithVerification(
    String email,
    String newPassword,
    String verificationCode,
  );

  // 用户信息相关方法
  Future<bool> changePassword(String currentPassword, String newPassword);
  Future<bool> updateUserInfo(String displayName);
  Future<bool> refreshUserProfile();
  Future<bool> forceRefreshUserProfile();

  // 会员管理方法
  Future<bool> upgradeToPremium(bool isOneTime, {DateTime? expiryDate});
  Future<bool> cancelPremium();
}
