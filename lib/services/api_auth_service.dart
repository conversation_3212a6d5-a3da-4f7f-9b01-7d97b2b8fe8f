import 'package:shared_preferences/shared_preferences.dart';
import 'dart:developer' as developer;
import '../models/user.dart';
import 'api_service.dart';
import 'base_auth_service.dart';
import 'database_service.dart';

class ApiAuthService extends BaseAuthService {
  static final ApiAuthService _instance = ApiAuthService._internal();
  factory ApiAuthService() => _instance;
  ApiAuthService._internal();

  final ApiService _apiService = ApiService();

  User? _currentUser;
  @override
  User? get currentUser => _currentUser;

  // 免费用户的最大物品数量限制
  static const int freeUserItemLimit = 30;

  // 检查用户是否已登录
  @override
  bool get isLoggedIn => _currentUser != null;

  // 检查是否为高级会员
  @override
  bool get isPremium {
    // 先检查用户类型
    final isPremiumType = _currentUser?.userType == UserType.premium;

    // 再检查到期日期（如果用户类型不是premium但有到期日期且未过期，也视为会员）
    final hasValidExpiry =
        _currentUser?.membershipExpiryDate != null &&
        !DateTime.now().isAfter(_currentUser!.membershipExpiryDate!.toLocal());

    return isPremiumType || hasValidExpiry;
  }

  // 检查会员是否已过期
  @override
  bool get isMembershipExpired {
    if (_currentUser == null) {
      return true;
    }

    if (_currentUser!.membershipExpiryDate == null) {
      return _currentUser!.userType !=
          UserType.premium; // 无到期日期但类型为premium表示永久会员
    }

    // 检查过期日期（将UTC时间转换为本地时间进行比较）
    return DateTime.now().isAfter(
      _currentUser!.membershipExpiryDate!.toLocal(),
    );
  }

  // 检查是否可以添加更多物品
  @override
  Future<bool> canAddMoreItems() async {
    // 如果是高级会员且未过期，无限制
    if (isPremium && !isMembershipExpired) {
      return true;
    }

    // 免费用户检查是否超过限制
    if (_currentUser != null) {
      final response = await _apiService.getUserItems(_currentUser!.id!);
      if (response.isSuccess && response.data != null) {
        return response.data!.length < freeUserItemLimit;
      }
    }

    return false;
  }

  // 获取免费用户剩余可添加物品数量
  @override
  Future<int> getRemainingItemQuota() async {
    if (isPremium && !isMembershipExpired) {
      return -1; // -1表示无限制
    }

    if (_currentUser != null) {
      final response = await _apiService.getUserItems(_currentUser!.id!);
      if (response.isSuccess && response.data != null) {
        final remaining = freeUserItemLimit - response.data!.length;
        return remaining > 0 ? remaining : 0;
      }
    }

    // 用户未登录，返回完整的配额
    return freeUserItemLimit;
  }

  // 初始化，检查用户是否已登录
  @override
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('api_token');

    if (token != null) {
      await refreshUserProfile();
    }
  }

  // 获取用户最新信息
  @override
  Future<bool> refreshUserProfile() async {
    developer.log('刷新用户信息', name: 'ApiAuthService');
    try {
      final response = await _apiService.getUserProfile();

      // 处理可能的401错误情况
      if (response.error != null && response.error!.contains('登录会话已过期')) {
        developer.log('刷新用户信息失败: 会话已过期', name: 'ApiAuthService');
        // 清空当前用户但不通知UI（避免循环）
        _currentUser = null;
        return false;
      }

      if (response.isSuccess && response.data != null) {
        // 检查会员状态是否变更
        final oldIsPremium = isPremium; // 使用getter而不是直接检查类型
        final oldExpiryDate = _currentUser?.membershipExpiryDate;
        final oldUser = _currentUser;

        _currentUser = response.data;

        final newIsPremium = isPremium; // 使用getter计算的结果
        final newExpiryDate = _currentUser?.membershipExpiryDate;

        // 显示本地时区的到期日期信息
        final oldLocalDate = oldExpiryDate?.toLocal();
        final newLocalDate = newExpiryDate?.toLocal();

        developer.log(
          '用户信息已更新: 会员状态: $oldIsPremium → $newIsPremium, '
          '到期日期: $oldLocalDate → $newLocalDate (本地时区)',
          name: 'ApiAuthService',
        );

        developer.log(
          '用户详情: 旧=$oldUser, 新=$_currentUser',
          name: 'ApiAuthService',
        );

        // 仅当状态变更时才通知监听器，避免不必要的UI更新
        if (oldIsPremium != newIsPremium || oldExpiryDate != newExpiryDate) {
          notifyListeners();
        }
        return true;
      } else {
        developer.log('刷新用户信息失败: ${response.error}', name: 'ApiAuthService');
        return false;
      }
    } catch (e) {
      developer.log('刷新用户信息异常: $e', name: 'ApiAuthService', error: e);
      return false;
    }
  }

  // 强制刷新用户资料（跳过缓存）
  @override
  Future<bool> forceRefreshUserProfile() async {
    developer.log('强制刷新用户信息（跳过缓存）', name: 'ApiAuthService');

    // 清除缓存
    _apiService.clearCache('user_profile');

    // 调用普通刷新方法
    return await refreshUserProfile();
  }

  // 发送注册验证码
  @override
  Future<bool> sendRegisterCode(String email) async {
    final response = await _apiService.requestVerificationCode(
      email,
      "register",
    );
    return response.isSuccess;
  }

  // 发送重置密码验证码
  @override
  Future<bool> sendResetPasswordCode(String email) async {
    final response = await _apiService.requestVerificationCode(
      email,
      "reset_password",
    );
    return response.isSuccess;
  }

  // 通过验证码重置密码
  @override
  Future<bool> resetPasswordWithVerification(
    String email,
    String newPassword,
    String verificationCode,
  ) async {
    developer.log(
      '开始重置密码: email=$email, 验证码长度=${verificationCode.length}',
      name: 'ApiAuthService',
    );

    try {
      // 调用重置密码API
      developer.log('调用重置密码API', name: 'ApiAuthService');
      final response = await _apiService.resetPassword(
        email,
        verificationCode,
        newPassword,
      );

      if (response.isSuccess) {
        developer.log(
          '密码重置成功: ${response.data?.message}',
          name: 'ApiAuthService',
        );
        return true;
      } else {
        developer.log('密码重置失败: ${response.error}', name: 'ApiAuthService');
        return false;
      }
    } catch (e) {
      developer.log('重置密码过程中出现异常: $e', name: 'ApiAuthService', error: e);
      return false;
    }
  }

  // 用户注册(带验证码验证)
  @override
  Future<bool> registerWithVerification(
    String email,
    String password,
    String displayName,
    String verificationCode,
  ) async {
    final response = await _apiService.register(
      email,
      password,
      displayName,
      verificationCode,
    );

    return response.isSuccess;
  }

  // 添加普通注册方法（实现BaseAuthService接口要求）
  @override
  Future<bool> register(
    String email,
    String password,
    String displayName,
  ) async {
    // API注册总是需要验证码，所以直接返回false
    // 客户端应该使用registerWithVerification方法
    return false;
  }

  // 用户登录
  @override
  Future<bool> login(String email, String password) async {
    developer.log('ApiAuthService.login() 开始: $email', name: 'ApiAuthService');

    // 检查网络连接
    bool isConnected = await _apiService.isNetworkConnected();
    if (!isConnected) {
      developer.log('网络连接不可用，无法登录', name: 'ApiAuthService');
      return false;
    }

    // 登录前保存当前本地用户ID，用于之后迁移数据
    final prefs = await SharedPreferences.getInstance();
    final localUserId = prefs.getString('current_user_id');
    developer.log('登录前的本地用户ID: $localUserId', name: 'ApiAuthService');

    developer.log('调用API登录接口', name: 'ApiAuthService');
    final response = await _apiService.login(email, password);
    developer.log(
      'API登录响应: isSuccess=${response.isSuccess}, 有数据=${response.data != null}',
      name: 'ApiAuthService',
    );

    if (response.isSuccess && response.data != null) {
      // 更新当前用户
      _currentUser = response.data!.user;
      developer.log(
        '登录成功，获取到用户: ${_currentUser?.displayName}, ID: ${_currentUser?.id}',
        name: 'ApiAuthService',
      );

      // 如果之前有本地用户ID，需要确保将该ID保存在SharedPreferences中
      // 这样即使在登录后，ItemProvider也能找到之前添加的物品
      if (localUserId != null && _currentUser != null) {
        developer.log('保留本地用户ID以确保数据连续性', name: 'ApiAuthService');
        // 此处不更新SharedPreferences中的current_user_id
        // 让ItemProvider仍然使用之前的用户ID加载物品

        // 修复：登录后尝试将本地物品关联到登录用户
        _migrateLocalItems(int.parse(localUserId), _currentUser!.id!);
      } else if (_currentUser?.id != null) {
        // 如果之前没有本地用户ID，则保存新的用户ID
        await prefs.setString('current_user_id', _currentUser!.id.toString());
        developer.log('保存新的用户ID: ${_currentUser!.id}', name: 'ApiAuthService');
      }

      notifyListeners();
      return true;
    }

    if (response.error != null) {
      developer.log('登录失败，错误信息: ${response.error}', name: 'ApiAuthService');
    } else {
      developer.log('登录失败: 未知原因', name: 'ApiAuthService');
    }
    return false;
  }

  // 添加物品迁移方法
  Future<void> _migrateLocalItems(int localUserId, int newUserId) async {
    developer.log(
      '开始迁移本地物品: 从临时ID $localUserId 到用户ID $newUserId',
      name: 'ApiAuthService',
    );

    try {
      final dbService = DatabaseService();

      // 先检查迁移前两个用户ID下分别有多少物品
      final localItems = await dbService.getUserItems(localUserId);
      final newUserItems = await dbService.getUserItems(newUserId);

      developer.log(
        '迁移前状态: 本地ID($localUserId)下有 ${localItems.length} 个物品, 新用户ID($newUserId)下有 ${newUserItems.length} 个物品',
        name: 'ApiAuthService',
      );

      if (localItems.isEmpty) {
        developer.log('没有物品需要迁移，检查本地物品存储情况', name: 'ApiAuthService');

        // 检查是否有未关联用户ID的物品
        final unlinkedItems = await dbService.getLocalItems();
        developer.log(
          '未关联用户ID的物品数量: ${unlinkedItems.length}',
          name: 'ApiAuthService',
        );

        if (unlinkedItems.isNotEmpty) {
          // 尝试将未关联的物品关联到新用户
          for (var item in unlinkedItems) {
            final updatedItem = item.copyWith(userId: newUserId);
            await dbService.updateItem(updatedItem);
            developer.log(
              '已将未关联物品 ${item.name} 关联到用户ID $newUserId',
              name: 'ApiAuthService',
            );
          }
        }

        // 直接返回，不继续迁移
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('current_user_id', newUserId.toString());
        developer.log(
          '已更新SharedPreferences中的用户ID为 $newUserId',
          name: 'ApiAuthService',
        );
        return;
      }

      // 更新本地物品的用户ID
      final count = await dbService.updateItemsUserId(localUserId, newUserId);

      developer.log('已迁移 $count 个物品到用户ID $newUserId', name: 'ApiAuthService');

      // 检查迁移后的结果
      final migratedItems = await dbService.getUserItems(newUserId);
      developer.log(
        '迁移后检查: 用户ID $newUserId 现在有 ${migratedItems.length} 个物品',
        name: 'ApiAuthService',
      );

      // 更新SharedPreferences中的用户ID为新ID
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('current_user_id', newUserId.toString());
      developer.log(
        '已更新SharedPreferences中的用户ID为 $newUserId',
        name: 'ApiAuthService',
      );
    } catch (e) {
      developer.log('迁移物品时出错: $e', name: 'ApiAuthService', error: e);
    }
  }

  // 用户登出
  @override
  Future<void> logout() async {
    // 清除token
    await _apiService.clearToken();

    // 清除用户资料缓存
    _apiService.clearCache('user_profile');

    // 清除当前用户
    _currentUser = null;
    notifyListeners();
  }

  // 获取用户信息
  Future<User?> getUserProfile() async {
    final response = await _apiService.getUserProfile();

    if (response.isSuccess && response.data != null) {
      _currentUser = response.data;
      notifyListeners();
      return _currentUser;
    }

    return null;
  }

  // 获取最后错误
  String getLastError() {
    // TODO: 实现错误处理逻辑
    return "操作失败，请稍后重试";
  }

  // 修改用户密码
  @override
  Future<bool> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    // TODO: 实现API修改密码逻辑
    // 由于目前API版本未实现此功能，返回false
    return false;
  }

  // 更新用户信息
  @override
  Future<bool> updateUserInfo(String displayName) async {
    // TODO: 实现API更新用户信息逻辑
    // 由于目前API版本未实现此功能，返回false
    return false;
  }

  // 升级为高级会员
  @override
  Future<bool> upgradeToPremium(bool isOneTime, {DateTime? expiryDate}) async {
    developer.log(
      '开始升级会员: isOneTime=$isOneTime, expiryDate=$expiryDate',
      name: 'ApiAuthService',
    );

    try {
      // 检查是否登录
      if (_currentUser == null) {
        developer.log('用户未登录，无法升级会员', name: 'ApiAuthService');
        return false;
      }

      // 由于后端没有实现升级会员接口，直接在本地处理
      developer.log('本地更新用户会员状态', name: 'ApiAuthService');

      // 更新本地用户信息
      _currentUser = _currentUser!.copyWith(
        userType: UserType.premium,
        membershipExpiryDate: expiryDate,
      );

      developer.log('本地用户会员信息已更新', name: 'ApiAuthService');
      notifyListeners();
      return true;
    } catch (e) {
      developer.log(
        '升级会员异常: ${e.toString()}',
        name: 'ApiAuthService',
        error: e,
      );
      return false;
    }
  }

  // 取消高级会员
  @override
  Future<bool> cancelPremium() async {
    developer.log('开始取消会员', name: 'ApiAuthService');

    try {
      // 检查是否登录
      if (_currentUser == null) {
        developer.log('用户未登录，无法取消会员', name: 'ApiAuthService');
        return false;
      }

      // 由于后端没有实现取消会员接口，直接在本地处理
      developer.log('本地更新用户会员状态', name: 'ApiAuthService');

      // 更新本地用户信息
      _currentUser = _currentUser!.copyWith(
        userType: UserType.free,
        membershipExpiryDate: null,
      );

      developer.log('本地用户会员信息已更新', name: 'ApiAuthService');
      notifyListeners();
      return true;
    } catch (e) {
      developer.log(
        '取消会员异常: ${e.toString()}',
        name: 'ApiAuthService',
        error: e,
      );
      return false;
    }
  }
}
