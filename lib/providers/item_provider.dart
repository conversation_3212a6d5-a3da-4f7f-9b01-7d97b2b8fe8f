import 'package:flutter/foundation.dart';
import 'dart:io';
import '../models/item.dart';
import '../services/database_service.dart';
import '../services/api_auth_service.dart';
import '../services/base_auth_service.dart';
import '../utils/image_helper.dart';
import 'base_item_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ItemProvider with ChangeNotifier implements BaseItemProvider {
  final DatabaseService _databaseService = DatabaseService();
  List<Item> _items = [];
  Map<String, List<Item>> _categorizedItems = {};
  bool _isLoading = false;
  String? _lastError;

  @override
  List<Item> get items => _items;
  @override
  Map<String, List<Item>> get categorizedItems => _categorizedItems;
  @override
  bool get isLoading => _isLoading;
  @override
  String? get lastError => _lastError;

  // 加载所有物品
  @override
  Future<void> loadItems() async {
    _isLoading = true;
    notifyListeners();

    try {
      // 获取当前用户ID
      final currentUserId = await _getCurrentUserId();

      // 无论用户是否登录，都尝试加载物品
      if (currentUserId != null) {
        // 从本地数据库加载特定用户的物品
        _items = await _databaseService.getUserItems(currentUserId);

        // 如果当前用户没有物品，尝试加载未关联的物品
        if (_items.isEmpty) {
          // 加载未关联物品
          List<Item> localItems = await _databaseService.getLocalItems();

          // 如果有未关联物品，尝试关联到当前用户
          if (localItems.isNotEmpty) {
            for (var item in localItems) {
              // 创建关联用户的新物品对象
              final updatedItem = item.copyWith(userId: currentUserId);
              // 更新到数据库
              await _databaseService.updateItem(updatedItem);
            }

            // 重新加载物品
            _items = await _databaseService.getUserItems(currentUserId);
          }
        }
      } else {
        // 加载未关联用户ID的物品（本地存储的物品）
        _items = await _databaseService.getLocalItems();
      }

      // 处理分类
      _categorizedItems = {};
      for (var item in _items) {
        if (_categorizedItems.containsKey(item.category)) {
          _categorizedItems[item.category]!.add(item);
        } else {
          _categorizedItems[item.category] = [item];
        }
      }
    } catch (e) {
      debugPrint('ItemProvider: 加载物品时出错: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 仅加载正常状态的物品
  @override
  Future<void> loadActiveItems() async {
    _isLoading = true;
    notifyListeners();

    try {
      // 获取当前用户ID
      final currentUserId = await _getCurrentUserId();

      List<Item> allItems = [];
      if (currentUserId != null) {
        // 从本地数据库加载特定用户的物品
        allItems = await _databaseService.getUserItems(currentUserId);
      } else {
        // 加载未关联用户ID的物品（本地存储的物品）
        allItems = await _databaseService.getLocalItems();
      }

      // 过滤活跃状态的物品
      _items =
          allItems.where((item) => item.status == ItemStatus.active).toList();

      // 重新组织分类列表
      _categorizedItems = {};
      for (var item in _items) {
        if (_categorizedItems.containsKey(item.category)) {
          _categorizedItems[item.category]!.add(item);
        } else {
          _categorizedItems[item.category] = [item];
        }
      }
    } catch (e) {
      debugPrint('ItemProvider: 加载活跃物品时出错: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 添加物品
  @override
  Future<void> addItem(Item item, {File? imageFile}) async {
    try {
      // 获取当前用户ID
      final currentUserId = await _getCurrentUserId();

      // 创建包含用户ID的新物品对象
      final itemWithUserId = item.copyWith(userId: currentUserId);

      final id = await _databaseService.insertItem(itemWithUserId);

      final newItem = itemWithUserId.copyWith(id: id);

      _items.add(newItem);

      // 更新分类列表
      if (_categorizedItems.containsKey(newItem.category)) {
        _categorizedItems[newItem.category]!.add(newItem);
      } else {
        _categorizedItems[newItem.category] = [newItem];
      }

      notifyListeners();
    } catch (e) {
      _lastError = '添加物品时出错: $e';
      debugPrint('ItemProvider: $_lastError');
    }
  }

  // 更新物品
  @override
  Future<void> updateItem(Item item, {File? imageFile}) async {
    try {
      await _databaseService.updateItem(item);

      // 更新本地列表
      final index = _items.indexWhere((i) => i.id == item.id);
      if (index != -1) {
        _items[index] = item;
      }

      // 更新分类列表
      await _updateCategorizedItems();

      notifyListeners();
    } catch (e) {
      _lastError = '更新物品时出错: $e';
      debugPrint('ItemProvider: $_lastError');
    }
  }

  // 删除物品
  @override
  Future<void> deleteItem(int id) async {
    try {
      debugPrint('ItemProvider: 开始删除物品 ID: $id');

      // 查找物品，以获取图片路径
      final itemIndex = _items.indexWhere((item) => item.id == id);

      // 如果物品存在
      if (itemIndex != -1) {
        final item = _items[itemIndex];
        debugPrint('ItemProvider: 找到要删除的物品: ${item.name}');

        // 删除关联的图片文件（如果有）
        if (item.imagePath != null && item.imagePath!.isNotEmpty) {
          await ImageHelper.deleteImageFile(item.imagePath);
          debugPrint('ItemProvider: 已删除关联图片: ${item.imagePath}');
        }

        // 从数据库删除物品
        await _databaseService.deleteItem(id);
        debugPrint('ItemProvider: 从数据库删除物品成功');

        // 从本地列表移除
        _items.removeAt(itemIndex);
        debugPrint('ItemProvider: 从内存列表移除物品');

        // 更新分类列表
        await _updateCategorizedItems();
        debugPrint('ItemProvider: 已更新分类列表');

        // 通知监听器数据已更改
        notifyListeners();
        debugPrint('ItemProvider: 删除物品完成，当前物品数量: ${_items.length}');
      } else {
        debugPrint('ItemProvider: 未找到ID为$id的物品');
      }
    } catch (e) {
      debugPrint('ItemProvider: 删除物品时出错: $e');
    }
  }

  // 更新物品状态
  @override
  Future<void> updateItemStatus(int itemId, ItemStatus status) async {
    try {
      await _databaseService.updateItemStatus(itemId, status);

      // 更新本地列表
      final index = _items.indexWhere((item) => item.id == itemId);
      if (index != -1) {
        final item = _items[index];
        _items[index] = item.copyWith(status: status);
      }

      // 更新分类列表
      await _updateCategorizedItems();

      notifyListeners();
    } catch (e) {
      debugPrint('ItemProvider: 更新物品状态时出错: $e');
    }
  }

  // 获取物品详情
  @override
  Future<Item?> getItemDetails(int itemId) async {
    try {
      return await _databaseService.getItem(itemId);
    } catch (e) {
      _lastError = '获取物品详情时出错: $e';
      debugPrint('ItemProvider: $_lastError');
      return null;
    }
  }

  // 更新物品转出价格（同时将状态设置为停用）
  @override
  Future<void> updateItemSellPrice(
    int itemId,
    double sellPrice, {
    DateTime? transferDate,
  }) async {
    try {
      // 先更新数据库
      await _databaseService.updateItemSellPrice(itemId, sellPrice);

      // 更新本地列表
      final index = _items.indexWhere((item) => item.id == itemId);
      if (index != -1) {
        final item = _items[index];
        _items[index] = item.copyWith(
          status: ItemStatus.inactive, // 转出时自动设置为停用状态
          sellPrice: sellPrice,
          transferDate: transferDate ?? DateTime.now(),
        );
      }

      // 更新分类列表
      await _updateCategorizedItems();

      notifyListeners();
    } catch (e) {
      _lastError = '更新物品转出价格时出错: $e';
      debugPrint('ItemProvider: $_lastError');
    }
  }

  // 获取活跃物品数量
  Future<int> getActiveItemCount() async {
    return await _databaseService.getActiveItemCount();
  }

  // 获取每日成本最高的物品（仅考虑活跃物品）
  @override
  List<Item> getTopDailyCostItems({int limit = 5}) {
    // 过滤出活跃物品
    final activeItems =
        _items.where((item) => item.status == ItemStatus.active).toList();

    // 排序并返回指定数量
    activeItems.sort(
      (a, b) =>
          (b.effectiveDailyCost ?? 0.0).compareTo(a.effectiveDailyCost ?? 0.0),
    );
    return activeItems.take(limit).toList();
  }

  // 获取总价值最高的物品
  @override
  List<Item> getTopValueItems({int limit = 5}) {
    final sortedItems = List<Item>.from(_items);
    sortedItems.sort((a, b) => b.price.compareTo(a.price));
    return sortedItems.take(limit).toList();
  }

  // 获取特定分类的物品
  @override
  List<Item> getItemsByCategory(String category) {
    return _categorizedItems[category] ?? [];
  }

  // 获取所有分类
  @override
  List<String> get categories => _categorizedItems.keys.toList();

  // 计算所有物品的总成本
  @override
  double get totalCost {
    return _items.fold<double>(0.0, (sum, item) => sum + item.price);
  }

  // 计算总日成本（仅考虑活跃物品）
  @override
  double get averageDailyCost {
    // 过滤出活跃物品
    final activeItems =
        _items.where((item) => item.status == ItemStatus.active).toList();

    if (activeItems.isEmpty) return 0.0;

    // 计算总日成本：所有活跃物品的日成本总和（不再除以物品数量）
    return activeItems.fold<double>(
      0.0,
      (sum, item) => sum + (item.effectiveDailyCost ?? 0.0),
    );
  }

  // 更新分类列表的辅助方法
  Future<void> _updateCategorizedItems() async {
    _categorizedItems = await _databaseService.getItemsByCategory();
  }

  // 加载模拟数据（仅用于演示）
  Future<void> loadMockItems() async {
    _isLoading = true;
    notifyListeners();

    try {
      // 演示数据功能已被移除
    } catch (e) {
      debugPrint('ItemProvider: 加载示例数据失败: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 获取当前用户ID
  Future<int?> _getCurrentUserId() async {
    try {
      // 1. 首先尝试从Auth服务获取
      final authService = await _getAuthService();
      final user = authService.currentUser;

      if (user != null && user.id != null) {
        // user.id在User模型中是int?类型，所以直接返回
        return user.id;
      }

      // 2. 如果Auth服务没有返回用户ID，尝试从SharedPreferences获取
      final prefs = await SharedPreferences.getInstance();
      final savedUserId = prefs.getString('current_user_id');

      if (savedUserId != null && savedUserId.isNotEmpty) {
        // 尝试转换为int
        try {
          return int.parse(savedUserId);
        } catch (e) {
          debugPrint('ItemProvider: 保存的用户ID转换为int失败: $savedUserId, 错误: $e');
        }
      }

      return null;
    } catch (e) {
      debugPrint('ItemProvider: 获取用户ID时出错: $e');
      return null;
    }
  }

  // 获取认证服务的辅助方法
  Future<BaseAuthService> _getAuthService() async {
    // 在本例中，我们总是返回ApiAuthService的单例实例
    return ApiAuthService();
  }

  // 实现updateUserId方法
  @override
  Future<void> updateUserId(int userId) async {
    try {
      // 保存用户ID到本地供后续使用
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('current_user_id', userId.toString());

      // 重新加载该用户的物品
      loadItems();
    } catch (e) {
      debugPrint('ItemProvider: 更新用户ID时出错: $e');
    }
  }
}
