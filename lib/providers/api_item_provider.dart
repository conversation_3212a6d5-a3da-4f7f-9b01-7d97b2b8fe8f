import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/item.dart';
import '../services/api_service.dart';
import 'base_item_provider.dart';

class ApiItemProvider with ChangeNotifier implements BaseItemProvider {
  final ApiService _apiService = ApiService();
  List<Item> _items = [];
  Map<String, List<Item>> _categorizedItems = {};
  bool _isLoading = false;
  String? _lastError;
  int? _currentUserId;
  bool _isOffline = false;

  @override
  List<Item> get items => _items;
  @override
  Map<String, List<Item>> get categorizedItems => _categorizedItems;
  @override
  bool get isLoading => _isLoading;
  @override
  String? get lastError => _lastError;
  bool get isOffline => _isOffline;

  // 清除错误信息
  void clearError() {
    _lastError = null;
    notifyListeners();
  }

  // 检查网络状态并设置离线模式
  Future<bool> _checkNetworkAndSetMode() async {
    final isConnected = await _apiService.isNetworkConnected();
    _isOffline = !isConnected;
    return isConnected;
  }

  // 保存数据到本地缓存
  Future<void> _saveItemsToCache() async {
    if (_currentUserId == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final itemsJson = jsonEncode(_items.map((item) => item.toMap()).toList());
      await prefs.setString('cached_items_$_currentUserId', itemsJson);
    } catch (e) {
      debugPrint('ApiItemProvider: 缓存物品数据失败: $e');
    }
  }

  // 从本地缓存加载数据
  Future<void> _loadItemsFromCache() async {
    if (_currentUserId == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final itemsJson = prefs.getString('cached_items_$_currentUserId');

      if (itemsJson != null) {
        final List<dynamic> decodedItems = jsonDecode(itemsJson);
        _items = decodedItems.map((item) => Item.fromMap(item)).toList();
        _updateCategorizedItems();
      }
    } catch (e) {
      debugPrint('ApiItemProvider: 从缓存加载物品数据失败: $e');
    }
  }

  // 加载所有物品
  @override
  Future<void> loadItems() async {
    // 如果用户没有登录，无法加载物品
    if (_currentUserId == null) {
      _lastError = "使用数据同步功能需要先登录";
      notifyListeners();
      return;
    }

    _isLoading = true;
    notifyListeners();

    // 检查网络连接
    final isConnected = await _checkNetworkAndSetMode();

    if (isConnected) {
      // 联网状态：从API加载数据
      await loadUserItems(_currentUserId!);
    } else {
      // 离线状态：从缓存加载数据
      await _loadItemsFromCache();
      _lastError = "当前处于离线模式，显示的是缓存数据";
      _isLoading = false;
      notifyListeners();
    }
  }

  // 仅加载活跃状态的物品
  @override
  Future<void> loadActiveItems() async {
    _isLoading = true;
    notifyListeners();

    // 如果用户没有登录，也可以加载（从缓存）
    if (_currentUserId == null) {
      _lastError = "使用数据同步功能需要先登录";
      _isLoading = false;
      notifyListeners();
      return;
    }

    // 检查网络连接
    final isConnected = await _checkNetworkAndSetMode();

    try {
      if (isConnected) {
        // 联网状态：从API加载
        final response = await _apiService.getUserItems(_currentUserId!);

        if (response.isSuccess && response.data != null) {
          // 过滤出活跃物品
          _items =
              response.data!
                  .where((item) => item.status == ItemStatus.active)
                  .toList();
          _updateCategorizedItems();

          // 保存到缓存
          await _saveItemsToCache();
        } else {
          _lastError = response.error ?? '加载物品失败';
        }
      } else {
        // 离线状态：从缓存加载
        await _loadItemsFromCache();
        // 过滤活跃物品
        _items =
            _items.where((item) => item.status == ItemStatus.active).toList();
        _updateCategorizedItems();
        _lastError = "当前处于离线模式，显示的是缓存数据";
      }
    } catch (e) {
      _lastError = '加载物品时出错: $e';
      debugPrint('ApiItemProvider: $_lastError');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 设置当前用户ID
  void setCurrentUserId(int? userId) {
    _currentUserId = userId;
    if (userId != null) {
      _loadItemsFromCache(); // 用户ID更改时，尝试加载缓存
    } else {
      _items = []; // 用户注销时，清空物品列表
      _updateCategorizedItems();
    }
  }

  // 加载指定用户的物品
  Future<void> loadUserItems(int userId) async {
    _isLoading = true;
    notifyListeners();

    // 检查网络连接
    final isConnected = await _checkNetworkAndSetMode();

    try {
      if (isConnected) {
        // 联网状态：从API加载
        final response = await _apiService.getUserItems(userId);

        if (response.isSuccess && response.data != null) {
          _items = response.data!;
          _updateCategorizedItems();

          // 保存到缓存
          await _saveItemsToCache();
        } else {
          _lastError = response.error ?? '加载物品失败';
        }
      } else {
        // 离线状态：从缓存加载
        await _loadItemsFromCache();
        _lastError = "当前处于离线模式，显示的是缓存数据";
      }
    } catch (e) {
      _lastError = '加载物品时出错: $e';
      debugPrint('ApiItemProvider: $_lastError');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 获取物品详情
  @override
  Future<Item?> getItemDetails(int itemId) async {
    try {
      final response = await _apiService.getItemDetails(itemId);

      if (response.isSuccess && response.data != null) {
        return response.data;
      } else {
        _lastError = response.error ?? '获取物品详情失败';
        return null;
      }
    } catch (e) {
      _lastError = '获取物品详情时出错: $e';
      debugPrint('ApiItemProvider: $_lastError');
      return null;
    }
  }

  // 添加物品
  @override
  Future<Item?> addItem(Item item, {File? imageFile}) async {
    _isLoading = true;
    notifyListeners();

    // 检查网络连接
    final isConnected = await _checkNetworkAndSetMode();
    if (!isConnected) {
      _lastError = "离线模式下无法添加物品，请连接网络后重试";
      _isLoading = false;
      notifyListeners();
      return null;
    }

    try {
      final response = await _apiService.addItem(item, imageFile: imageFile);

      if (response.isSuccess && response.data != null) {
        // 添加到本地缓存
        _items.add(response.data!);
        _updateCategorizedItems();
        await _saveItemsToCache();
        return response.data;
      } else {
        _lastError = response.error ?? '添加物品失败';
        return null;
      }
    } catch (e) {
      _lastError = '添加物品时出错: $e';
      debugPrint('ApiItemProvider: $_lastError');
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 更新物品
  @override
  Future<Item?> updateItem(Item item, {File? imageFile}) async {
    _isLoading = true;
    notifyListeners();

    // 检查网络连接
    final isConnected = await _checkNetworkAndSetMode();
    if (!isConnected) {
      _lastError = "离线模式下无法更新物品，请连接网络后重试";
      _isLoading = false;
      notifyListeners();
      return null;
    }

    try {
      final response = await _apiService.updateItem(item, imageFile: imageFile);

      if (response.isSuccess && response.data != null) {
        // 更新本地缓存
        final index = _items.indexWhere((i) => i.id == item.id);
        if (index != -1) {
          _items[index] = response.data!;
          _updateCategorizedItems();
          await _saveItemsToCache();
        }
        return response.data;
      } else {
        _lastError = response.error ?? '更新物品失败';
        return null;
      }
    } catch (e) {
      _lastError = '更新物品时出错: $e';
      debugPrint('ApiItemProvider: $_lastError');
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 删除物品
  @override
  Future<bool> deleteItem(int itemId) async {
    _isLoading = true;
    notifyListeners();

    // 检查网络连接
    final isConnected = await _checkNetworkAndSetMode();
    if (!isConnected) {
      _lastError = "离线模式下无法删除物品，请连接网络后重试";
      _isLoading = false;
      notifyListeners();
      return false;
    }

    try {
      final response = await _apiService.deleteItem(itemId);

      if (response.isSuccess) {
        // 从本地缓存中移除
        _items.removeWhere((item) => item.id == itemId);
        _updateCategorizedItems();
        await _saveItemsToCache();
        return true;
      } else {
        _lastError = response.error ?? '删除物品失败';
        return false;
      }
    } catch (e) {
      _lastError = '删除物品时出错: $e';
      debugPrint('ApiItemProvider: $_lastError');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 根据物品状态更新物品
  @override
  Future<Item?> updateItemStatus(int itemId, ItemStatus status) async {
    // 首先获取当前物品
    final currentItem = _items.firstWhere(
      (item) => item.id == itemId,
      orElse: () => throw Exception('物品不存在'),
    );

    // 创建更新后的物品副本
    final updatedItem = currentItem.copyWith(status: status);

    // 调用更新方法
    return await updateItem(updatedItem);
  }

  // 更新物品转出价格和状态
  @override
  Future<Item?> updateItemSellPrice(
    int itemId,
    double sellPrice, {
    DateTime? transferDate,
  }) async {
    // 首先获取当前物品
    final currentItem = _items.firstWhere(
      (item) => item.id == itemId,
      orElse: () => throw Exception('物品不存在'),
    );

    // 创建更新后的物品副本
    final updatedItem = currentItem.copyWith(
      status: ItemStatus.inactive,
      sellPrice: sellPrice,
      transferDate: transferDate ?? DateTime.now(),
    );

    // 调用更新方法
    return await updateItem(updatedItem);
  }

  // 获取每日成本最高的物品（仅考虑活跃物品）
  @override
  List<Item> getTopDailyCostItems({int limit = 5}) {
    // 过滤出活跃物品
    final activeItems =
        _items.where((item) => item.status == ItemStatus.active).toList();

    // 排序并返回指定数量
    activeItems.sort(
      (a, b) =>
          (b.effectiveDailyCost ?? 0.0).compareTo(a.effectiveDailyCost ?? 0.0),
    );
    return activeItems.take(limit).toList();
  }

  // 获取总价值最高的物品
  @override
  List<Item> getTopValueItems({int limit = 5}) {
    final sortedItems = List<Item>.from(_items);
    sortedItems.sort((a, b) => b.price.compareTo(a.price));
    return sortedItems.take(limit).toList();
  }

  // 获取特定分类的物品
  @override
  List<Item> getItemsByCategory(String category) {
    return _categorizedItems[category] ?? [];
  }

  // 获取所有分类
  @override
  List<String> get categories => _categorizedItems.keys.toList();

  // 计算所有物品的总成本
  @override
  double get totalCost {
    return _items.fold<double>(0.0, (sum, item) => sum + item.price);
  }

  // 计算总日成本（仅考虑活跃物品）
  @override
  double get averageDailyCost {
    // 过滤出活跃物品
    final activeItems =
        _items.where((item) => item.status == ItemStatus.active).toList();

    if (activeItems.isEmpty) return 0.0;

    // 计算总日成本：所有活跃物品的日成本总和（不再除以物品数量）
    return activeItems.fold<double>(
      0.0,
      (sum, item) => sum + (item.effectiveDailyCost ?? 0.0),
    );
  }

  // 更新分类列表
  void _updateCategorizedItems() {
    _categorizedItems = {};
    for (var item in _items) {
      if (_categorizedItems.containsKey(item.category)) {
        _categorizedItems[item.category]!.add(item);
      } else {
        _categorizedItems[item.category] = [item];
      }
    }
  }

  // 更新用户ID
  @override
  Future<void> updateUserId(int userId) async {
    try {
      _currentUserId = userId;
      // 保存用户ID到SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('current_user_id', userId.toString());

      // 加载该用户的物品
      await loadUserItems(userId);
    } catch (e) {
      debugPrint('ApiItemProvider: 更新用户ID时出错: $e');
    }
  }
}
