import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:developer' as developer;

enum AppThemeMode { light, dark, system }

class AppSettingsProvider with ChangeNotifier {
  // 默认值
  AppThemeMode _themeMode = AppThemeMode.system;
  Locale? _locale = const Locale('zh'); // 默认使用中文
  static const String themeKey = 'app_theme';
  static const String localeKey = 'app_locale';

  // 获取当前主题模式
  AppThemeMode get themeMode => _themeMode;

  // 获取当前语言区域
  Locale? get locale => _locale;

  // 检查是否使用系统默认区域
  bool get isSystemLocale => _locale == null;

  // 构造函数
  AppSettingsProvider() {
    _loadSettings();
  }

  // 从SharedPreferences加载设置
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 加载主题设置
      final String? themeModeString = prefs.getString(themeKey);
      if (themeModeString != null) {
        if (themeModeString == 'light') {
          _themeMode = AppThemeMode.light;
        } else if (themeModeString == 'dark') {
          _themeMode = AppThemeMode.dark;
        } else {
          _themeMode = AppThemeMode.system;
        }
      }

      // 加载语言设置
      final String? localeString = prefs.getString(localeKey);
      if (localeString != null && localeString != 'system') {
        final parts = localeString.split('_');
        _locale = Locale(parts[0], parts.length > 1 ? parts[1] : '');
      } else {
        _locale = null; // 使用系统默认
      }

      developer.log(
        '加载设置: 主题=${_themeMode.name}, 语言=${_locale?.toString() ?? '系统默认'}',
        name: 'AppSettings',
      );

      notifyListeners();
    } catch (e) {
      developer.log('加载设置出错: $e', name: 'AppSettings', error: e);
    }
  }

  // 设置主题模式
  Future<void> setThemeMode(AppThemeMode mode) async {
    if (_themeMode == mode) return;

    _themeMode = mode;
    notifyListeners();

    // 保存设置
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(themeKey, mode.name);
      developer.log('保存主题设置: ${mode.name}', name: 'AppSettings');
    } catch (e) {
      developer.log('保存主题设置出错: $e', name: 'AppSettings', error: e);
    }
  }

  // 设置语言区域
  Future<void> setLocale(Locale? newLocale) async {
    // 如果设置相同则不处理
    if ((_locale == null && newLocale == null) ||
        (_locale != null &&
            newLocale != null &&
            _locale!.languageCode == newLocale.languageCode)) {
      return;
    }

    _locale = newLocale;
    notifyListeners();

    // 保存设置
    try {
      final prefs = await SharedPreferences.getInstance();
      if (newLocale == null) {
        await prefs.setString(localeKey, 'system');
        developer.log('保存语言设置: 系统默认', name: 'AppSettings');
      } else {
        final localeString =
            newLocale.countryCode == null || newLocale.countryCode!.isEmpty
                ? newLocale.languageCode
                : '${newLocale.languageCode}_${newLocale.countryCode}';
        await prefs.setString(localeKey, localeString);
        developer.log('保存语言设置: $localeString', name: 'AppSettings');
      }
    } catch (e) {
      developer.log('保存语言设置出错: $e', name: 'AppSettings', error: e);
    }
  }

  // 使用系统默认语言
  Future<void> useSystemLocale() async {
    await setLocale(null);
  }

  // 切换到英文
  Future<void> useEnglish() async {
    await setLocale(const Locale('en'));
  }

  // 切换到中文
  Future<void> useChinese() async {
    await setLocale(const Locale('zh'));
  }
}
