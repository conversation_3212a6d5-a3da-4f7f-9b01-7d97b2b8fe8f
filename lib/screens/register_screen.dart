import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/base_auth_service.dart';
import '../widgets/custom_button.dart';
import 'dart:async';
import 'package:calculate_cost_flutter/l10n/app_localizations.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  _RegisterScreenState createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _displayNameController = TextEditingController();
  final _verificationCodeController = TextEditingController();
  bool _isLoading = false;
  bool _isSendingCode = false;
  String? _errorMessage;
  int _countdown = 0;
  Timer? _timer;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _displayNameController.dispose();
    _verificationCodeController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  // 发送验证码
  Future<void> _sendVerificationCode() async {
    final localizations = AppLocalizations.of(context);
    // 验证邮箱
    final emailValidator = _validateEmail(_emailController.text);
    if (emailValidator != null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(emailValidator)));
      return;
    }

    setState(() {
      _isSendingCode = true;
      _errorMessage = null;
    });

    try {
      // 发送验证码
      final authService = Provider.of<BaseAuthService>(context, listen: false);
      final success = await authService.sendRegisterCode(
        _emailController.text.trim(),
      );

      // 处理结果
      if (success) {
        // 开始倒计时
        setState(() {
          _countdown = 60;
        });
        _startCountdownTimer();

        // 显示成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              localizations?.verificationCodeSent ?? '验证码已发送到邮箱，请查收',
            ),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // 发送失败
        setState(() {
          _errorMessage =
              localizations?.codeSendingFailed ?? '验证码发送失败，邮箱可能已被注册';
        });
      }
    } catch (e) {
      // 处理异常
      setState(() {
        _errorMessage =
            '${localizations?.codeSendingFailed ?? "验证码发送失败"}: ${e.toString()}';
      });
    } finally {
      // 无论成功失败，都需要取消加载状态
      setState(() {
        _isSendingCode = false;
      });
    }
  }

  // 开始倒计时
  void _startCountdownTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_countdown > 0) {
          _countdown--;
        } else {
          _timer?.cancel();
        }
      });
    });
  }

  String? _validateEmail(String? value) {
    final localizations = AppLocalizations.of(context);
    if (value == null || value.isEmpty) {
      return localizations?.pleaseEnterEmail ?? '请输入邮箱';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return localizations?.pleaseEnterValidEmail ?? '请输入有效的邮箱地址';
    }
    return null;
  }

  Future<void> _register() async {
    final localizations = AppLocalizations.of(context);
    // 验证表单
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 获取AuthService实例
      final authService = Provider.of<BaseAuthService>(context, listen: false);

      // 尝试注册
      final success = await authService.registerWithVerification(
        _emailController.text.trim(),
        _passwordController.text.trim(),
        _displayNameController.text.trim(),
        _verificationCodeController.text.trim(),
      );

      // 处理注册结果
      if (success) {
        // 注册成功，返回登录页
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(localizations?.registrationSuccess ?? '注册成功，请登录'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        }
      } else {
        // 注册失败
        setState(() {
          _errorMessage =
              localizations?.registrationFailed ?? '注册失败，验证码可能无效或已过期';
        });
      }
    } catch (e) {
      // 处理异常
      setState(() {
        _errorMessage =
            '${localizations?.registrationFailed ?? "注册失败"}: ${e.toString()}';
      });
    } finally {
      // 无论成功失败，都需要取消加载状态
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(title: Text(localizations?.register ?? '注册')),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 应用Logo或图标
                const Icon(Icons.calculate, size: 80, color: Colors.blue),
                const SizedBox(height: 16),

                // 应用名称
                const Text(
                  'CostTrack',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 32),

                // 错误信息显示
                if (_errorMessage != null)
                  Container(
                    padding: const EdgeInsets.all(8),
                    color: Colors.red.shade100,
                    child: Text(
                      _errorMessage!,
                      style: TextStyle(color: Colors.red.shade900),
                    ),
                  ),
                if (_errorMessage != null) const SizedBox(height: 16),

                // 用户名输入框
                TextFormField(
                  controller: _displayNameController,
                  decoration: InputDecoration(
                    labelText: localizations?.username ?? '用户名',
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.person),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return localizations?.pleaseEnterUsername ?? '请输入用户名';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // 邮箱输入框
                TextFormField(
                  controller: _emailController,
                  decoration: InputDecoration(
                    labelText: localizations?.email ?? '邮箱',
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.email),
                  ),
                  keyboardType: TextInputType.emailAddress,
                  validator: _validateEmail,
                ),
                const SizedBox(height: 16),

                // 验证码输入框
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _verificationCodeController,
                        decoration: InputDecoration(
                          labelText: localizations?.verificationCode ?? '验证码',
                          border: const OutlineInputBorder(),
                          prefixIcon: const Icon(Icons.security),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return localizations?.pleaseEnterVerificationCode ??
                                '请输入验证码';
                          }
                          if (value.length != 6) {
                            return '${localizations?.verificationCode ?? "验证码"}为6位数字';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      height: 56, // 与输入框同高
                      child: ElevatedButton(
                        onPressed:
                            _isSendingCode || _countdown > 0
                                ? null
                                : _sendVerificationCode,
                        style: ElevatedButton.styleFrom(
                          foregroundColor: Colors.white,
                          backgroundColor: Theme.of(context).primaryColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        child:
                            _isSendingCode
                                ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white,
                                    ),
                                    strokeWidth: 2,
                                  ),
                                )
                                : Text(
                                  _countdown > 0
                                      ? localizations?.retryAfterSeconds(
                                            _countdown,
                                          ) ??
                                          '${_countdown}s后重试'
                                      : localizations?.getVerificationCode ??
                                          '获取验证码',
                                ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // 密码输入框
                TextFormField(
                  controller: _passwordController,
                  decoration: InputDecoration(
                    labelText: localizations?.password ?? '密码',
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.lock),
                  ),
                  obscureText: true,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return localizations?.pleaseEnterPassword ?? '请输入密码';
                    }
                    if (value.length < 6) {
                      return localizations?.passwordMinLength ?? '密码长度不能少于6个字符';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // 确认密码输入框
                TextFormField(
                  controller: _confirmPasswordController,
                  decoration: InputDecoration(
                    labelText: localizations?.confirmPassword ?? '确认密码',
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.lock_outline),
                  ),
                  obscureText: true,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return localizations?.pleaseConfirmPassword ?? '请确认密码';
                    }
                    if (value != _passwordController.text) {
                      return localizations?.passwordsDoNotMatch ?? '两次输入的密码不一致';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // 注册按钮
                CustomButton(
                  text: localizations?.register ?? '注册',
                  onPressed: _isLoading ? null : _register,
                  isLoading: _isLoading,
                ),
                const SizedBox(height: 16),

                // 登录链接
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(localizations?.alreadyHaveAccount ?? '已有账号？'),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(localizations?.returnToLogin ?? '返回登录'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
