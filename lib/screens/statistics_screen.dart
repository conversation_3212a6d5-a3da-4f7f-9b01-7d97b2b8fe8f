import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:calculate_cost_flutter/l10n/app_localizations.dart';
import '../providers/base_item_provider.dart';
import '../utils/formatters.dart';
import '../utils/constants.dart';
import '../models/item.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _selectedTabIndex = 0;

  // 图表动画控制器
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _selectedTabIndex = _tabController.index;
      });
    });

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(100),
        child: _buildAppBar(context),
      ),
      body: Consumer<BaseItemProvider>(
        builder: (context, itemProvider, _) {
          if (itemProvider.items.isEmpty) {
            return _buildEmptyState(context);
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(context, itemProvider),
              _buildCategoriesTab(context, itemProvider),
              _buildTrendsTab(context, itemProvider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.secondary,
            ],
          ),
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(25),
            bottomRight: Radius.circular(25),
          ),
        ),
      ),

      bottom: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        indicatorWeight: 3,
        indicatorSize: TabBarIndicatorSize.label,
        labelStyle: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.normal,
          fontSize: 14,
        ),
        tabs: [
          Tab(text: localizations?.overview ?? 'Overview'),
          Tab(text: localizations?.category ?? 'Category'),
          Tab(text: localizations?.trends ?? 'Trends'),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 150,
            height: 150,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              shape: BoxShape.circle,
            ),
            child: Icon(Icons.bar_chart, size: 80, color: Colors.grey.shade400),
          ),
          const SizedBox(height: 24),
          Text(
            localizations?.noStatsData ?? 'No data available for statistics',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            localizations?.addItemsToSeeStats ?? 'Add items to see statistics',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.addItem);
            },
            icon: const Icon(Icons.add),
            label: Text(localizations?.addItem ?? 'Add Item'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 概览标签页
  Widget _buildOverviewTab(
    BuildContext context,
    BaseItemProvider itemProvider,
  ) {
    final localizations = AppLocalizations.of(context);
    final totalCost = itemProvider.totalCost;
    final averageDailyCost = itemProvider.averageDailyCost;

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // 主要统计卡片
        _buildMainStatsCard(context, totalCost, averageDailyCost),

        const SizedBox(height: 24),

        // 分类饼图
        _buildCategoryPieChart(context, itemProvider),

        const SizedBox(height: 24),

        // 使用时间分布
        _buildItemsTimeDistribution(context, itemProvider),
      ],
    );
  }

  // 主要统计卡片
  Widget _buildMainStatsCard(
    BuildContext context,
    double totalCost,
    double dailyCost,
  ) {
    final localizations = AppLocalizations.of(context);

    return Card(
      elevation: 8,
      shadowColor: Colors.black26,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primary.withOpacity(0.8),
              Theme.of(context).colorScheme.secondary.withOpacity(0.9),
            ],
          ),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 总投资卡片
                FadeTransition(
                  opacity: _animation,
                  child: _buildStatItem(
                    context,
                    localizations?.totalInvestment ?? 'Total Investment',
                    Formatters.formatCurrency(totalCost),
                    Icons.account_balance_wallet,
                    Colors.white,
                  ),
                ),

                // 每日成本卡片
                FadeTransition(
                  opacity: _animation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0.5, 0),
                      end: Offset.zero,
                    ).animate(_animation),
                    child: _buildStatItem(
                      context,
                      localizations?.averageDailyCost ?? 'Daily Cost',
                      Formatters.formatDailyCost(dailyCost, context),
                      Icons.calendar_today,
                      Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 单个统计项目
  Widget _buildStatItem(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color textColor,
  ) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.38,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, color: textColor, size: 18),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: textColor.withOpacity(0.8),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: textColor,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // 分类饼图卡片
  Widget _buildCategoryPieChart(
    BuildContext context,
    BaseItemProvider itemProvider,
  ) {
    final localizations = AppLocalizations.of(context);
    final categories = itemProvider.categories;
    final Map<String, double> categoryValues = {};
    double totalCost = 0.0;

    for (var category in categories) {
      final items = itemProvider.getItemsByCategory(category);
      final categoryCost = items.fold<double>(
        0.0,
        (sum, item) => sum + item.price,
      );
      categoryValues[category] = categoryCost;
      totalCost += categoryCost;
    }

    return Card(
      elevation: 4,
      shadowColor: Colors.black12,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localizations?.categoryDistribution ?? 'Category Distribution',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),

            SizedBox(
              height: 220,
              child: ScaleTransition(
                scale: _animation,
                child: PieChart(
                  PieChartData(
                    sections: _getSections(categoryValues),
                    centerSpaceRadius: 50,
                    sectionsSpace: 2,
                    borderData: FlBorderData(show: false),
                    pieTouchData: PieTouchData(enabled: true),
                    centerSpaceColor: Colors.transparent,
                  ),
                  swapAnimationDuration: const Duration(milliseconds: 800),
                  swapAnimationCurve: Curves.easeInOutQuint,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 图例
            Wrap(
              spacing: 16,
              runSpacing: 12,
              children: _buildLegendItems(categoryValues, totalCost),
            ),
          ],
        ),
      ),
    );
  }

  // 为饼图创建图例项
  List<Widget> _buildLegendItems(
    Map<String, double> categoryValues,
    double total,
  ) {
    final items = <Widget>[];
    final sortedEntries =
        categoryValues.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    final localizations = AppLocalizations.of(context);

    for (var entry in sortedEntries) {
      final percentage = total > 0 ? (entry.value / total * 100) : 0.0;
      final color = getCategoryColor(entry.key);

      // 获取本地化的分类名称
      String localizedCategory = Formatters.getCategoryName(entry.key, context);

      items.add(
        SizedBox(
          width: 150,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: color.withOpacity(0.4),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  localizedCategory,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                '${percentage.toStringAsFixed(1)}%',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return items;
  }

  // 使用时间分布
  Widget _buildItemsTimeDistribution(
    BuildContext context,
    BaseItemProvider itemProvider,
  ) {
    final localizations = AppLocalizations.of(context);
    final items = itemProvider.items;

    // 按使用天数分组
    final Map<String, int> timeDistribution = {
      '0-30 days': 0,
      '1-3 months': 0,
      '3-6 months': 0,
      '6-12 months': 0,
      '1+ years': 0,
    };

    for (var item in items) {
      if (item.daysUsed <= 30) {
        timeDistribution['0-30 days'] =
            (timeDistribution['0-30 days'] ?? 0) + 1;
      } else if (item.daysUsed <= 90) {
        timeDistribution['1-3 months'] =
            (timeDistribution['1-3 months'] ?? 0) + 1;
      } else if (item.daysUsed <= 180) {
        timeDistribution['3-6 months'] =
            (timeDistribution['3-6 months'] ?? 0) + 1;
      } else if (item.daysUsed <= 365) {
        timeDistribution['6-12 months'] =
            (timeDistribution['6-12 months'] ?? 0) + 1;
      } else {
        timeDistribution['1+ years'] = (timeDistribution['1+ years'] ?? 0) + 1;
      }
    }

    return Card(
      elevation: 4,
      shadowColor: Colors.black12,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localizations?.itemUsageTime ?? 'Item Usage Time',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),

            SizedBox(
              height: 200,
              child: SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0, 0.5),
                  end: Offset.zero,
                ).animate(_animation),
                child: BarChart(
                  BarChartData(
                    alignment: BarChartAlignment.spaceAround,
                    barTouchData: BarTouchData(enabled: true),
                    titlesData: FlTitlesData(
                      show: true,
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          getTitlesWidget: (value, meta) {
                            const titles = [
                              '0-30 days',
                              '1-3 months',
                              '3-6 months',
                              '6-12 months',
                              '1+ years',
                            ];
                            return Padding(
                              padding: const EdgeInsets.only(top: 8),
                              child: Text(
                                titles[value.toInt() % titles.length],
                                style: const TextStyle(fontSize: 10),
                              ),
                            );
                          },
                          reservedSize: 28,
                        ),
                      ),
                      leftTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          getTitlesWidget: (value, meta) {
                            if (value % 1 != 0) return const SizedBox();
                            return Text(
                              value.toInt().toString(),
                              style: const TextStyle(fontSize: 10),
                            );
                          },
                          reservedSize: 28,
                        ),
                      ),
                      topTitles: AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      rightTitles: AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                    ),
                    gridData: FlGridData(
                      drawHorizontalLine: true,
                      drawVerticalLine: false,
                      horizontalInterval: 1,
                    ),
                    borderData: FlBorderData(show: false),
                    barGroups: [
                      _createBarGroup(
                        0,
                        timeDistribution['0-30 days'] ?? 0,
                        context,
                      ),
                      _createBarGroup(
                        1,
                        timeDistribution['1-3 months'] ?? 0,
                        context,
                      ),
                      _createBarGroup(
                        2,
                        timeDistribution['3-6 months'] ?? 0,
                        context,
                      ),
                      _createBarGroup(
                        3,
                        timeDistribution['6-12 months'] ?? 0,
                        context,
                      ),
                      _createBarGroup(
                        4,
                        timeDistribution['1+ years'] ?? 0,
                        context,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 创建柱状图组
  BarChartGroupData _createBarGroup(int x, int y, BuildContext context) {
    return BarChartGroupData(
      x: x,
      barRods: [
        BarChartRodData(
          toY: y.toDouble(),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.secondary,
            ],
          ),
          width: 16,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(6),
            topRight: Radius.circular(6),
          ),
        ),
      ],
    );
  }

  // 分类标签页
  Widget _buildCategoriesTab(
    BuildContext context,
    BaseItemProvider itemProvider,
  ) {
    final localizations = AppLocalizations.of(context);
    final categories = itemProvider.categories;
    final Map<String, double> categoryValues = {};
    double totalCost = 0.0;

    for (var category in categories) {
      final items = itemProvider.getItemsByCategory(category);
      final categoryCost = items.fold<double>(
        0.0,
        (sum, item) => sum + item.price,
      );
      categoryValues[category] = categoryCost;
      totalCost += categoryCost;
    }

    // 排序分类（按金额降序）
    final sortedCategories =
        categories.toList()..sort(
          (a, b) =>
              (categoryValues[b] ?? 0.0).compareTo(categoryValues[a] ?? 0.0),
        );

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        Text(
          localizations?.categoryAnalysis ?? 'Category Analysis',
          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),

        // 分类列表
        ...sortedCategories.map((category) {
          final double value = categoryValues[category] ?? 0.0;
          final double percentage =
              totalCost > 0.0 ? (value / totalCost * 100.0) : 0.0;

          return _buildEnhancedCategoryItem(
            context,
            category,
            value,
            percentage,
            getCategoryColor(category),
            itemProvider.getItemsByCategory(category).length,
          );
        }),
      ],
    );
  }

  // 增强版分类项
  Widget _buildEnhancedCategoryItem(
    BuildContext context,
    String category,
    double value,
    double percentage,
    Color color,
    int itemCount,
  ) {
    final localizations = AppLocalizations.of(context);
    final Map<String, IconData> categoryIcons = {
      'Electronics': Icons.devices,
      'Shopping': Icons.shopping_cart,
      'Car': Icons.directions_car,
      'Pet': Icons.pets,
      'Food': Icons.fastfood,
      'Beauty': Icons.face,
      'Other': Icons.category,
    };

    return Card(
      elevation: 3,
      margin: const EdgeInsets.symmetric(vertical: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () {
          // 可以添加点击进入分类详情的功能
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [color.withOpacity(0.7), color],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: color.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Icon(
                      categoryIcons[category] ?? Icons.category,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          category,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${localizations?.items ?? 'Items'}: $itemCount | ${percentage.toStringAsFixed(1)}%',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    Formatters.formatCurrency(value),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: LinearProgressIndicator(
                  value: percentage / 100,
                  backgroundColor: Colors.grey.shade200,
                  valueColor: AlwaysStoppedAnimation<Color>(color),
                  minHeight: 8,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 趋势标签页
  Widget _buildTrendsTab(BuildContext context, BaseItemProvider itemProvider) {
    final localizations = AppLocalizations.of(context);

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        Text(
          localizations?.costTrends ?? 'Cost Trends',
          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 20),

        // 使用条形图替代之前的趋势线图
        Card(
          elevation: 4,
          shadowColor: Colors.black12,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      localizations?.monthlyExpenditure ??
                          'Monthly Expenditure',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Icon(
                      Icons.bar_chart,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                SizedBox(
                  height: 250,
                  child: FadeTransition(
                    opacity: _animation,
                    child: _buildMonthlyBarChart(context, itemProvider),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 创建月度条形图
  Widget _buildMonthlyBarChart(
    BuildContext context,
    BaseItemProvider itemProvider,
  ) {
    final localizations = AppLocalizations.of(context);

    // 计算当前月份和前5个月
    List<String> months = [];

    // 获取当前月份
    final now = DateTime.now();
    final currentMonth = now.month;

    // 计算过去6个月（按从左到右的时间顺序，从最早到最近）
    for (int i = 5; i >= 0; i--) {
      int month =
          (currentMonth - i) <= 0
              ? (currentMonth - i + 12)
              : (currentMonth - i);

      // 获取本地化的月份名称
      String monthName = '';
      switch (month) {
        case 1:
          monthName = localizations?.monthAbbr1 ?? 'Jan';
          break;
        case 2:
          monthName = localizations?.monthAbbr2 ?? 'Feb';
          break;
        case 3:
          monthName = localizations?.monthAbbr3 ?? 'Mar';
          break;
        case 4:
          monthName = localizations?.monthAbbr4 ?? 'Apr';
          break;
        case 5:
          monthName = localizations?.monthAbbr5 ?? 'May';
          break;
        case 6:
          monthName = localizations?.monthAbbr6 ?? 'Jun';
          break;
        case 7:
          monthName = localizations?.monthAbbr7 ?? 'Jul';
          break;
        case 8:
          monthName = localizations?.monthAbbr8 ?? 'Aug';
          break;
        case 9:
          monthName = localizations?.monthAbbr9 ?? 'Sep';
          break;
        case 10:
          monthName = localizations?.monthAbbr10 ?? 'Oct';
          break;
        case 11:
          monthName = localizations?.monthAbbr11 ?? 'Nov';
          break;
        case 12:
          monthName = localizations?.monthAbbr12 ?? 'Dec';
          break;
      }

      months.add(monthName);
    }

    // 计算每个月的成本
    List<double> monthlyCosts = [];

    // 获取活跃物品的每日总成本和总价值
    final activeItems =
        itemProvider.items
            .where((item) => item.status == ItemStatus.active)
            .toList();

    // 计算每日总成本
    final dailyTotalCost = activeItems.fold(
      0.0,
      (sum, item) => sum + (item.effectiveDailyCost ?? 0.0),
    );

    // 计算物品总价值
    final totalItemValue = activeItems.fold(
      0.0,
      (sum, item) => sum + item.price,
    );

    // 基于每日成本合理预估每月成本，避免过大值
    // 月度成本上限为物品总价值的1/6 (约6个月可均摊完总成本)
    final maxMonthlyAmount = totalItemValue / 6;

    // 每月天数
    List<int> daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

    // 计算每月成本
    for (int i = 0; i < 6; i++) {
      // 获取对应月份天数
      int monthIndex =
          (currentMonth - 5 + i) <= 0
              ? (currentMonth - 5 + i + 12) % 12
              : (currentMonth - 5 + i) % 12;

      if (monthIndex == 0) monthIndex = 12;

      final daysCount = daysInMonth[monthIndex - 1];

      // 计算当月成本 (日均成本 x 天数，但不超过合理上限)
      double monthCost = dailyTotalCost * daysCount;
      monthCost = monthCost > maxMonthlyAmount ? maxMonthlyAmount : monthCost;

      monthlyCosts.add(monthCost);
    }

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY:
            monthlyCosts.isNotEmpty
                ? monthlyCosts.reduce((a, b) => a > b ? a : b) * 1.2
                : totalItemValue,
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              return BarTooltipItem(
                Formatters.formatCurrency(monthlyCosts[groupIndex]),
                const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (double value, TitleMeta meta) {
                final index = value.toInt();
                if (index >= 0 && index < months.length) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      months[index],
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.black54,
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (double value, TitleMeta meta) {
                return SizedBox(
                  width: 40,
                  child: Text(
                    '￥${value.toInt()}',
                    style: const TextStyle(fontSize: 10, color: Colors.black54),
                    textAlign: TextAlign.right,
                  ),
                );
              },
            ),
          ),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        barGroups: List.generate(
          months.length,
          (index) => BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: monthlyCosts[index],
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primary.withOpacity(0.7),
                    Theme.of(context).colorScheme.secondary,
                  ],
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                ),
                width: 22,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6),
                ),
              ),
            ],
          ),
        ),
        gridData: FlGridData(show: false),
      ),
    );
  }

  // 为饼图生成扇区
  List<PieChartSectionData> _getSections(Map<String, double> categoryValues) {
    final sections = <PieChartSectionData>[];
    double totalValue = categoryValues.values.fold(
      0.0,
      (sum, value) => sum + value,
    );

    if (totalValue <= 0) {
      // 如果总值为0，返回一个默认的灰色部分
      sections.add(
        PieChartSectionData(
          color: Colors.grey.shade300,
          value: 1,
          title: '',
          radius: 50,
          titleStyle: const TextStyle(fontSize: 0),
        ),
      );
      return sections;
    }

    int index = 0;
    categoryValues.forEach((category, value) {
      final double percentage = value / totalValue;
      final color = getCategoryColor(category);

      sections.add(
        PieChartSectionData(
          color: color,
          value: value,
          title:
              percentage > 0.1
                  ? '${(percentage * 100).toStringAsFixed(0)}%'
                  : '',
          titleStyle: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 14,
            shadows: [Shadow(color: Colors.black26, blurRadius: 2)],
          ),
          radius: 60,
          borderSide: BorderSide(color: Colors.white, width: 2),
        ),
      );

      index++;
    });

    return sections;
  }

  // 获取分类颜色
  Color getCategoryColor(String category) {
    final Map<String, Color> categoryColors = {
      'Electronics': Colors.blueAccent,
      'Shopping': const Color(0xFF26C6DA),
      'Car': const Color(0xFFEF5350),
      'Pet': const Color(0xFF66BB6A),
      'Food': const Color(0xFFFFCA28),
      'Beauty': const Color(0xFFAB47BC),
      'Other': const Color(0xFF78909C),
    };

    return categoryColors[category] ??
        Color.fromARGB(
          255,
          150 + (category.hashCode % 100),
          100 + (category.hashCode % 150),
          200 - (category.hashCode % 100),
        );
  }
}
