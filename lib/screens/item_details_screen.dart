import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:calculate_cost_flutter/l10n/app_localizations.dart';
import '../models/item.dart';
import '../providers/base_item_provider.dart';
import '../utils/formatters.dart';
import '../utils/constants.dart';

class ItemDetailsScreen extends StatelessWidget {
  final Item item;

  const ItemDetailsScreen({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return Consumer<BaseItemProvider>(
      builder: (context, itemProvider, _) {
        // 从provider中获取最新的物品数据
        final updatedItem = itemProvider.items.firstWhere(
          (i) => i.id == item.id,
          orElse: () => item, // 如果未找到则使用原始物品
        );

        return Scaffold(
          appBar: AppBar(
            title: Text(updatedItem.name),
            backgroundColor: Theme.of(context).colorScheme.inversePrimary,
            actions: [
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () {
                  // 导航到编辑页面
                  Navigator.pushNamed(
                    context,
                    '/edit-item',
                    arguments: updatedItem,
                  );
                },
              ),
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () => _showDeleteConfirmation(context, updatedItem),
              ),
            ],
          ),
          body: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildImageHeader(context, updatedItem),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildItemHeader(context, updatedItem),
                      const SizedBox(height: 24),
                      _buildDetailsCard(context, updatedItem),
                      const SizedBox(height: 24),
                      _buildCostAnalysisCard(context, updatedItem),
                      if (updatedItem.notes != null &&
                          updatedItem.notes!.isNotEmpty)
                        const SizedBox(height: 24),
                      if (updatedItem.notes != null &&
                          updatedItem.notes!.isNotEmpty)
                        _buildNotesCard(context, updatedItem),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildImageHeader(BuildContext context, Item item) {
    // 获取此分类的颜色
    final Color categoryColor =
        ItemCategories.categoryColors[item.category] ??
        Theme.of(context).colorScheme.primary;

    // 获取图标
    final IconData categoryIcon =
        ItemCategories.categoryIcons[item.category] ?? Icons.help_outline;

    // 始终显示分类图标而非图片
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            categoryColor.withOpacity(0.3),
            categoryColor.withOpacity(0.1),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(categoryIcon, size: 80, color: categoryColor),
            const SizedBox(height: 16),
            Text(
              Formatters.getCategoryName(item.category, context),
              style: TextStyle(
                color: categoryColor,
                fontWeight: FontWeight.w500,
                fontSize: 18,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemHeader(BuildContext context, Item item) {
    final localizations = AppLocalizations.of(context);
    final categoryName = Formatters.getCategoryName(item.category, context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item.name,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${localizations?.categoryTitle ?? '分类'}: $categoryName',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              Formatters.formatCurrency(item.price),
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                Formatters.formatDailyCost(item.dailyCost, context),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDetailsCard(BuildContext context, Item item) {
    final localizations = AppLocalizations.of(context);

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localizations?.basicInfo ?? '基本信息',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildDetailRow(
              localizations?.purchaseDate ?? '购买日期',
              Formatters.formatLongDate(item.purchaseDate),
            ),
            const Divider(),
            _buildDetailRow(
              localizations?.daysUsed ?? '使用天数',
              '${item.daysUsed} ${localizations?.days ?? '天'}',
            ),
            const Divider(),
            _buildDetailRow(
              localizations?.purchasePrice ?? '购买价格',
              Formatters.formatCurrency(item.price),
            ),
            if (item.sellPrice != null) const Divider(),
            if (item.sellPrice != null)
              _buildDetailRow(
                localizations?.transferAmount ?? '转出价格',
                Formatters.formatCurrency(item.sellPrice!),
              ),
            // 显示转让/停用/损坏日期
            if (item.status != ItemStatus.active && item.transferDate != null)
              const Divider(),
            if (item.status != ItemStatus.active && item.transferDate != null)
              _buildDetailRow(
                item.status == ItemStatus.transfer
                    ? (localizations?.transferDateTitle ?? '转让日期')
                    : item.status == ItemStatus.damaged
                    ? (localizations?.damageDate ?? '损坏日期')
                    : (localizations?.inactiveDate ?? '停用日期'),
                Formatters.formatLongDate(item.transferDate!),
              ),
            const Divider(),
            _buildStatusRow(context, item),
          ],
        ),
      ),
    );
  }

  Widget _buildCostAnalysisCard(BuildContext context, Item item) {
    final localizations = AppLocalizations.of(context);
    bool isInactive = item.status != ItemStatus.active;

    // 计算周/月/年成本，不超过物品总价值
    double dailyCost = item.dailyCost ?? 0.0;
    double weeklyCost = dailyCost * 7;
    double monthlyCost = dailyCost * 30;
    double yearlyCost = dailyCost * 365;

    // 确保长期成本不超过物品总价值（或净成本）
    double maxCost = item.sellPrice != null ? item.netCost : item.price;
    weeklyCost = weeklyCost > maxCost ? maxCost : weeklyCost;
    monthlyCost = monthlyCost > maxCost ? maxCost : monthlyCost;
    yearlyCost = yearlyCost > maxCost ? maxCost : yearlyCost;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  localizations?.costAnalysis ?? '成本分析',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (isInactive) const SizedBox(width: 8),
                if (isInactive)
                  Tooltip(
                    message: '此物品${item.status.name}，成本仅供参考，不会计入总统计',
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(item).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getLocalizedStatus(context, item.status),
                        style: TextStyle(
                          fontSize: 12,
                          color: _getStatusColor(item),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            // 添加净成本信息（如果已转出）
            if (item.isSold)
              Column(
                children: [
                  _buildDetailRow(
                    localizations?.netCost ?? '净成本',
                    Formatters.formatCurrency(item.netCost),
                  ),
                  const Divider(),
                ],
              ),
            _buildDetailRow(
              localizations?.dailyCost ?? '每日成本',
              Formatters.formatDailyCost(dailyCost, context),
            ),
            const Divider(),
            _buildDetailRow(
              '${Formatters.formatCurrency(weeklyCost)}/${localizations?.week ?? "周"}',
              '',
            ),
            const Divider(),
            _buildDetailRow(
              '${Formatters.formatCurrency(monthlyCost)}/${localizations?.month ?? "月"}',
              '',
            ),
            const Divider(),
            _buildDetailRow(
              '${Formatters.formatCurrency(yearlyCost)}/${localizations?.year ?? "年"}',
              '',
            ),
            if (yearlyCost == maxCost)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  item.isSold
                      ? '${localizations?.netCostCapNote ?? "注：长期成本已按物品净成本"} (${Formatters.formatCurrency(maxCost)}) ${localizations?.capped ?? "封顶"}'
                      : '${localizations?.costCapNote ?? "注：长期成本已按物品总价值"} (${Formatters.formatCurrency(maxCost)}) ${localizations?.capped ?? "封顶"}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard(BuildContext context, Item item) {
    final localizations = AppLocalizations.of(context);

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localizations?.notes ?? '备注',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(item.notes ?? '', style: const TextStyle(fontSize: 16)),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(fontSize: 16, color: Colors.grey[700])),
          Text(
            value,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRow(BuildContext context, Item item) {
    final localizations = AppLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            localizations?.itemStatus ?? '物品状态',
            style: TextStyle(fontSize: 16, color: Colors.grey[700]),
          ),
          InkWell(
            onTap: () => _showStatusChangeDialog(context, item),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _getStatusColor(item).withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _getLocalizedStatus(context, item.status),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _getStatusColor(item),
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(Icons.edit, size: 14, color: _getStatusColor(item)),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(Item item) {
    switch (item.status) {
      case ItemStatus.active:
        return Colors.green;
      case ItemStatus.damaged:
        return Colors.orange;
      case ItemStatus.inactive:
        return Colors.red;
      case ItemStatus.transfer:
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  void _showDeleteConfirmation(BuildContext context, Item item) {
    final localizations = AppLocalizations.of(context);

    // 检查item.id是否为空
    if (item.id == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('无法删除物品：ID为空')));
      return;
    }

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(localizations?.confirmDelete ?? '确认删除'),
          content: Text(
            localizations?.deleteConfirmation(item.name) ??
                '您确定要删除 "${item.name}" 吗？此操作不可撤销。',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(localizations?.cancel ?? '取消'),
            ),
            TextButton(
              onPressed: () {
                final itemProvider = Provider.of<BaseItemProvider>(
                  context,
                  listen: false,
                );
                itemProvider.deleteItem(item.id!);
                Navigator.pop(context); // 关闭对话框
                Navigator.pop(context); // 返回上一页
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: Text(localizations?.delete ?? '删除'),
            ),
          ],
        );
      },
    );
  }

  String _getLocalizedStatus(BuildContext context, ItemStatus status) {
    final localizations = AppLocalizations.of(context);
    switch (status) {
      case ItemStatus.active:
        return localizations?.statusActive ?? '正常使用中';
      case ItemStatus.damaged:
        return localizations?.statusDamaged ?? '已损坏';
      case ItemStatus.inactive:
        return localizations?.statusInactive ?? '已停用';
      case ItemStatus.transfer:
        return localizations?.statusTransfer ?? '已转让';
      default:
        return status.name;
    }
  }

  void _showStatusChangeDialog(BuildContext context, Item item) {
    final localizations = AppLocalizations.of(context);
    // 默认使用当前日期
    DateTime statusChangeDate = DateTime.now();

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(localizations?.changeStatus ?? '修改物品状态'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ListTile(
                    title: Text(localizations?.statusActive ?? '正常使用中'),
                    leading: const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                    ),
                    onTap:
                        () => _updateItemStatus(
                          context,
                          item,
                          ItemStatus.active,
                          null, // 正常状态不需要转让日期
                          null, // 正常状态不需要转让金额
                        ),
                  ),
                  ListTile(
                    title: Text(localizations?.statusDamaged ?? '已损坏'),
                    leading: const Icon(Icons.error, color: Colors.orange),
                    onTap:
                        () => _showDatePicker(
                          context,
                          item,
                          ItemStatus.damaged,
                          statusChangeDate,
                        ),
                  ),
                  ListTile(
                    title: Text(localizations?.statusInactive ?? '已停用'),
                    leading: const Icon(Icons.cancel, color: Colors.red),
                    onTap:
                        () => _showDatePicker(
                          context,
                          item,
                          ItemStatus.inactive,
                          statusChangeDate,
                        ),
                  ),
                  ListTile(
                    title: Text(localizations?.statusTransfer ?? '已转让'),
                    leading: const Icon(Icons.transform, color: Colors.blue),
                    onTap:
                        () => _showDatePicker(
                          context,
                          item,
                          ItemStatus.transfer,
                          statusChangeDate,
                        ),
                  ),
                  const SizedBox(height: 8),
                  // 显示状态说明
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Text(
                      localizations?.statusDescription ??
                          '状态说明：\n正常使用中 - 物品正常计入每日成本\n已损坏 - 物品已损坏，不再计入成本\n已停用 - 物品已不再使用，不再计入成本\n已转让 - 物品已转让给他人，不再计入成本',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(localizations?.cancel ?? '取消'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // 显示日期选择器
  void _showDatePicker(
    BuildContext context,
    Item item,
    ItemStatus newStatus,
    DateTime initialDate,
  ) {
    final localizations = AppLocalizations.of(context);

    // 关闭状态选择对话框
    Navigator.pop(context);

    // 如果是转让状态，先显示转让金额输入对话框
    if (newStatus == ItemStatus.transfer) {
      _showTransferAmountDialog(context, item, initialDate);
      return;
    }

    // 弹出日期选择器
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            newStatus == ItemStatus.damaged
                ? (localizations?.damageDate ?? '损坏日期')
                : (localizations?.inactiveDate ?? '停用日期'),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                localizations?.statusDateDescription ??
                    '请选择状态变更日期，将用于计算物品的实际使用成本',
                style: TextStyle(fontSize: 14, color: Colors.grey[700]),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () async {
                  final DateTime? picked = await showDatePicker(
                    context: context,
                    initialDate: initialDate,
                    firstDate: item.purchaseDate,
                    lastDate: DateTime.now(),
                  );
                  if (picked != null && context.mounted) {
                    _updateItemStatus(context, item, newStatus, picked, null);
                  }
                },
                child: Text(localizations?.selectDate ?? '选择日期'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(localizations?.cancel ?? '取消'),
            ),
            TextButton(
              onPressed: () {
                // 使用当前日期
                _updateItemStatus(
                  context,
                  item,
                  newStatus,
                  DateTime.now(),
                  null,
                );
              },
              child: Text(localizations?.useCurrentDate ?? '使用当前日期'),
            ),
          ],
        );
      },
    );
  }

  // 显示转让金额输入对话框
  void _showTransferAmountDialog(
    BuildContext context,
    Item item,
    DateTime initialDate,
  ) {
    final localizations = AppLocalizations.of(context);
    final TextEditingController amountController = TextEditingController(
      text: item.sellPrice?.toString() ?? '',
    );

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(localizations?.transferItem ?? '物品转让'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                localizations?.transferDescription ??
                    '请输入转让时收到的金额，这将帮助计算物品的实际成本。',
                style: TextStyle(fontSize: 14, color: Colors.grey[700]),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: amountController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: localizations?.transferAmount ?? '转让金额',
                  prefixText: '¥',
                  border: const OutlineInputBorder(),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(localizations?.cancel ?? '取消'),
            ),
            TextButton(
              onPressed: () {
                // 验证金额
                double? amount;
                try {
                  amount = double.parse(amountController.text);
                  if (amount < 0) throw Exception('金额不能为负');
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        localizations?.pleaseEnterValidTransferAmount ??
                            '请输入有效的转让金额',
                      ),
                    ),
                  );
                  return;
                }

                // 关闭对话框
                Navigator.pop(context);

                // 显示日期选择器
                showDialog(
                  context: context,
                  builder: (context) {
                    return AlertDialog(
                      title: Text(localizations?.transferDateTitle ?? '转让日期'),
                      content: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            localizations?.statusDateDescription ??
                                '请选择状态变更日期，将用于计算物品的实际使用成本',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[700],
                            ),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () async {
                              final DateTime? picked = await showDatePicker(
                                context: context,
                                initialDate: initialDate,
                                firstDate: item.purchaseDate,
                                lastDate: DateTime.now(),
                              );
                              if (picked != null && context.mounted) {
                                _updateItemStatus(
                                  context,
                                  item,
                                  ItemStatus.transfer,
                                  picked,
                                  amount,
                                );
                              }
                            },
                            child: Text(localizations?.selectDate ?? '选择日期'),
                          ),
                        ],
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: Text(localizations?.cancel ?? '取消'),
                        ),
                        TextButton(
                          onPressed: () {
                            // 使用当前日期
                            _updateItemStatus(
                              context,
                              item,
                              ItemStatus.transfer,
                              DateTime.now(),
                              amount,
                            );
                          },
                          child: Text(
                            localizations?.useCurrentDate ?? '使用当前日期',
                          ),
                        ),
                      ],
                    );
                  },
                );
              },
              child: Text(localizations?.confirmTransfer ?? '确认转让'),
            ),
          ],
        );
      },
    );
  }

  void _updateItemStatus(
    BuildContext context,
    Item item,
    ItemStatus newStatus,
    DateTime? statusChangeDate,
    double? transferAmount,
  ) {
    if (item.status == newStatus &&
        (newStatus == ItemStatus.active || statusChangeDate == null) &&
        (newStatus != ItemStatus.transfer ||
            transferAmount == null ||
            transferAmount == item.sellPrice)) {
      Navigator.pop(context);
      return;
    }

    // 检查item.id是否为空
    if (item.id == null) {
      Navigator.pop(context);
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('无法更新物品：ID为空')));
      return;
    }

    // 创建新的物品对象，包含更新的状态和转让日期
    // 只有非活跃状态设置转让日期
    Item updatedItem = Item(
      id: item.id,
      name: item.name,
      price: item.price,
      category: item.category,
      purchaseDate: item.purchaseDate,
      status: newStatus,
      notes: item.notes,
      imagePath: item.imagePath,
      // 如果是转让状态且提供了金额，使用新金额，否则保持原金额
      sellPrice:
          newStatus == ItemStatus.transfer && transferAmount != null
              ? transferAmount
              : item.sellPrice,
      // 如果是活跃状态则清除转让日期，否则使用传入的日期或保持原日期
      transferDate:
          newStatus == ItemStatus.active
              ? null
              : (statusChangeDate ?? item.transferDate),
    );

    // 更新物品状态
    final itemProvider = Provider.of<BaseItemProvider>(context, listen: false);
    itemProvider.updateItem(updatedItem);

    // 关闭对话框
    if (context.mounted) {
      Navigator.pop(context);

      // 显示成功信息
      String message;
      final localizations = AppLocalizations.of(context);

      if (newStatus == ItemStatus.active) {
        message = localizations?.itemStatusActiveSuccess ?? '物品已设为正常使用状态';
      } else if (newStatus == ItemStatus.transfer) {
        final amount = Formatters.formatCurrency(transferAmount ?? 0);
        final date = Formatters.formatShortDate(
          statusChangeDate ?? DateTime.now(),
        );

        if (localizations?.itemStatusTransferSuccess != null) {
          message = localizations!.itemStatusTransferSuccess(amount, date);
        } else {
          message = '物品已转让，转让金额：$amount，使用结束日期：$date';
        }
      } else {
        final date = Formatters.formatShortDate(
          statusChangeDate ?? DateTime.now(),
        );

        if (localizations?.itemStatusChangeSuccess != null) {
          message = localizations!.itemStatusChangeSuccess(date);
        } else {
          message = '物品状态已更新，使用结束日期：$date';
        }
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), duration: const Duration(seconds: 2)),
      );
    }
  }
}
