import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:developer' as developer;
import '../services/base_auth_service.dart';
import '../services/api_service.dart';
import '../widgets/custom_button.dart';
import 'register_screen.dart';
import 'reset_password_screen.dart';
import 'home_screen.dart';
import '../providers/base_item_provider.dart';
import 'package:calculate_cost_flutter/l10n/app_localizations.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  bool _isNetworkConnected = true;

  @override
  void initState() {
    super.initState();
    _checkNetworkStatus();
  }

  Future<void> _checkNetworkStatus() async {
    final apiService = ApiService();
    final isConnected = await apiService.isNetworkConnected();

    if (mounted) {
      setState(() {
        _isNetworkConnected = isConnected;
      });
    }
  }

  @override
  void dispose() {
    developer.log('LoginScreen销毁', name: 'LoginScreen');
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _login() async {
    developer.log('开始登录流程', name: 'LoginScreen');
    // 验证表单
    if (!_formKey.currentState!.validate()) {
      developer.log('表单验证失败', name: 'LoginScreen');
      return;
    }

    // 如果网络不可用，显示错误消息
    if (!_isNetworkConnected) {
      final localizations = AppLocalizations.of(context);
      setState(() {
        _errorMessage = localizations?.networkUnavailable ?? '网络连接不可用，请检查网络设置';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    String email = _emailController.text.trim();
    String password = _passwordController.text.trim();

    developer.log('尝试登录: $email', name: 'LoginScreen');

    try {
      // 获取物品提供者实例，用于后续处理
      final itemProvider = Provider.of<BaseItemProvider>(
        context,
        listen: false,
      );

      // 获取AuthService实例
      final authService = Provider.of<BaseAuthService>(context, listen: false);
      developer.log(
        '获取到AuthService实例: ${authService.runtimeType}',
        name: 'LoginScreen',
      );

      // 尝试登录
      developer.log('调用login方法', name: 'LoginScreen');
      final success = await authService.login(email, password);
      developer.log('登录结果: $success', name: 'LoginScreen');

      // 处理登录结果
      if (success) {
        developer.log('登录成功，导航到主页', name: 'LoginScreen');

        // 获取并记录用户ID
        final user = authService.currentUser;
        developer.log(
          '登录用户: ${user?.displayName}, ID: ${user?.id}',
          name: 'LoginScreen',
        );

        // 重新加载物品列表
        developer.log('准备重新加载物品数据', name: 'LoginScreen');

        try {
          // 等待足够长的时间确保数据库迁移完成
          await Future.delayed(const Duration(seconds: 1));

          // 显式调用物品提供者的updateUserId方法以确保数据迁移
          if (user?.id != null) {
            developer.log('显式更新物品提供者的用户ID: ${user!.id}', name: 'LoginScreen');
            await itemProvider.updateUserId(user.id!);
          }

          // 再次加载物品确保最新数据
          developer.log('再次加载物品数据', name: 'LoginScreen');
          await itemProvider.loadItems();

          // 检查是否成功加载物品
          developer.log(
            '加载完成，物品数量: ${itemProvider.items.length}',
            name: 'LoginScreen',
          );
        } catch (e) {
          developer.log('加载物品数据时出错: $e', name: 'LoginScreen', error: e);
        }

        // 导航到主页 - 使用pushAndRemoveUntil确保清除路由栈
        developer.log('导航到HomeScreen', name: 'LoginScreen');
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/home',
          (route) => false, // 清除所有之前的路由
        );
      } else {
        // 登录失败
        final localizations = AppLocalizations.of(context);
        developer.log('登录失败: 邮箱或密码不正确', name: 'LoginScreen');
        setState(() {
          _errorMessage = localizations?.invalidEmailOrPassword ?? '邮箱或密码不正确';
        });
      }
    } catch (e) {
      // 处理异常
      final localizations = AppLocalizations.of(context);
      developer.log('登录过程中发生异常: $e', name: 'LoginScreen', error: e);
      setState(() {
        _errorMessage = localizations?.loginFailed ?? '登录失败: ${e.toString()}';
      });
    } finally {
      // 无论成功失败，都需要取消加载状态
      developer.log('登录流程结束', name: 'LoginScreen');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    developer.log('构建LoginScreen', name: 'LoginScreen');
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(title: Text(localizations?.login ?? '登录')),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 应用Logo或图标
                const Icon(Icons.calculate, size: 80, color: Colors.blue),
                const SizedBox(height: 16),

                // 应用名称
                const Text(
                  'CostTrack',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 32),

                // 网络状态指示
                if (!_isNetworkConnected)
                  Container(
                    padding: const EdgeInsets.all(8),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.wifi_off, color: Colors.orange.shade900),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            localizations?.offlineLoginWarning ??
                                '当前网络不可用，登录后将无法同步数据',
                            style: TextStyle(color: Colors.orange.shade900),
                          ),
                        ),
                        TextButton(
                          onPressed: () async {
                            setState(() {
                              _isLoading = true;
                            });
                            await _checkNetworkStatus();
                            setState(() {
                              _isLoading = false;
                            });
                          },
                          child: Text(localizations?.retry ?? '重试'),
                        ),
                      ],
                    ),
                  ),

                // 错误信息显示
                if (_errorMessage != null)
                  Container(
                    padding: const EdgeInsets.all(8),
                    color: Colors.red.shade100,
                    child: Text(
                      _errorMessage!,
                      style: TextStyle(color: Colors.red.shade900),
                    ),
                  ),
                if (_errorMessage != null) const SizedBox(height: 16),

                // 邮箱输入框
                TextFormField(
                  controller: _emailController,
                  decoration: InputDecoration(
                    labelText: localizations?.email ?? '邮箱',
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.email),
                  ),
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return localizations?.pleaseEnterEmail ?? '请输入邮箱';
                    }
                    if (!RegExp(
                      r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                    ).hasMatch(value)) {
                      return localizations?.pleaseEnterValidEmail ??
                          '请输入有效的邮箱地址';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // 密码输入框
                TextFormField(
                  controller: _passwordController,
                  decoration: InputDecoration(
                    labelText: localizations?.password ?? '密码',
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.lock),
                  ),
                  obscureText: true,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return localizations?.pleaseEnterPassword ?? '请输入密码';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // 登录按钮
                CustomButton(
                  text: localizations?.login ?? '登录',
                  onPressed: _isLoading ? null : _login,
                  isLoading: _isLoading,
                ),
                const SizedBox(height: 16),

                // 注册链接
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(localizations?.noAccount ?? '还没有账号？'),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const RegisterScreen(),
                          ),
                        );
                      },
                      child: Text(localizations?.registerNow ?? '立即注册'),
                    ),
                  ],
                ),

                // 忘记密码链接
                TextButton(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const ResetPasswordScreen(),
                      ),
                    );
                  },
                  child: Text(localizations?.forgotPassword ?? '忘记密码？'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
