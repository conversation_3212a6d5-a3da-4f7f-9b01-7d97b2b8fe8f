import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:calculate_cost_flutter/l10n/app_localizations.dart';
import '../models/item.dart';
import '../providers/base_item_provider.dart';
import '../widgets/item_card.dart';
import '../widgets/summary_card.dart';
import '../screens/profile_screen.dart';
import '../screens/statistics_screen.dart';
import '../services/api_auth_service.dart';
import '../utils/formatters.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  List<Item> _filteredItems = [];
  bool _isFiltered = false;
  String _selectedCategory = '';
  bool _isCategoryFiltered = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 在第一次构建完成后，加载数据
      _loadItems();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // 加载数据
  void _loadItems() {
    final provider = Provider.of<BaseItemProvider>(context, listen: false);
    provider.loadItems();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations?.appTitle ?? 'CostTrack'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_currentIndex < 2)
            PopupMenuButton<String>(
              icon: const Icon(Icons.filter_list),
              onSelected: (value) => _filterByStatus(context, value),
              itemBuilder:
                  (context) => [
                    PopupMenuItem(
                      value: 'all',
                      child: Text(localizations?.showAllItems ?? '显示所有物品'),
                    ),
                    PopupMenuItem(
                      value: 'active',
                      child: Text(localizations?.showActiveItems ?? '仅显示活跃物品'),
                    ),
                  ],
            ),
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        type: BottomNavigationBarType.fixed,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
          // 切换标签时重新加载数据，确保各个页面数据一致
          if (index < 3) {
            // 主页、分类和统计页面都需要最新数据
            _loadItems();
            debugPrint('导航切换到索引: $index，重新加载物品数据');
          }
        },
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.home),
            label: localizations?.home ?? '主页',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.category),
            label: localizations?.category ?? '分类',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.bar_chart),
            label: localizations?.statistics ?? '统计',
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.person),
            label: localizations?.profile ?? '我的',
          ),
        ],
      ),
      floatingActionButton:
          _currentIndex < 2
              ? FloatingActionButton(
                onPressed: () => Navigator.pushNamed(context, '/add-item'),
                child: const Icon(Icons.add),
              )
              : null,
    );
  }

  Widget _buildBody() {
    final itemProvider = Provider.of<BaseItemProvider>(context);
    final localizations = AppLocalizations.of(context);

    if (_currentIndex == 3) {
      return const ProfileScreen();
    }

    if (_currentIndex == 2) {
      return const StatisticsScreen();
    }

    if (itemProvider.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (itemProvider.items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.shopping_bag_outlined,
              size: 80,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              localizations?.noItems ?? '您还没有添加任何物品',
              style: const TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => Navigator.pushNamed(context, '/add-item'),
              icon: const Icon(Icons.add),
              label: Text(localizations?.addFirstItem ?? '添加第一个物品'),
            ),
          ],
        ),
      );
    }

    if (_currentIndex == 0) {
      return _buildHomeTab(itemProvider.items);
    } else {
      return _buildCategoryTab(itemProvider.categorizedItems);
    }
  }

  Widget _buildHomeTab(List<Item> items) {
    final itemProvider = Provider.of<BaseItemProvider>(context, listen: false);
    final localizations = AppLocalizations.of(context);

    // 应用搜索过滤
    final filteredItems = _getFilteredItems(items);

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        SummaryCard(
          totalCost: itemProvider.totalCost,
          averageDailyCost: itemProvider.averageDailyCost,
          itemCount: items.length,
        ),
        const SizedBox(height: 20),

        // 搜索和筛选部分
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Container(
                height: 44,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(22),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Row(
                  children: [
                    Icon(Icons.search, color: Colors.grey.shade500),
                    const SizedBox(width: 8),
                    Expanded(
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText:
                              localizations?.itemName ?? 'Search item name',
                          hintStyle: TextStyle(color: Colors.grey.shade500),
                          border: InputBorder.none,
                          isDense: true,
                          contentPadding: EdgeInsets.zero,
                        ),
                        style: const TextStyle(fontSize: 14),
                        onChanged: (value) {
                          setState(() {
                            _searchQuery = value;
                          });
                          _filterItems();
                        },
                      ),
                    ),
                    if (_searchQuery.isNotEmpty)
                      GestureDetector(
                        onTap: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                          _filterItems();
                        },
                        child: Icon(
                          Icons.clear,
                          color: Colors.grey.shade600,
                          size: 16,
                        ),
                      ),
                  ],
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    const SizedBox(width: 8),
                    _buildFilterButton(
                      localizations?.categoryTitle ?? '分类',
                      Icons.category_outlined,
                      '▼',
                      () => _showCategoryFilter(context),
                    ),
                    const SizedBox(width: 8),
                    _buildFilterButton(
                      localizations?.itemStatus ?? 'All statuses',
                      Icons.check_circle_outline,
                      '▼',
                      () => _showStatusFilter(context),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),
        _buildEnhancedItemsList(filteredItems),
      ],
    );
  }

  Widget _buildFilterButton(
    String text,
    IconData? icon,
    String indicator,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          children: [
            if (icon != null) ...[
              Icon(icon, size: 16, color: Colors.grey.shade700),
              const SizedBox(width: 4),
            ],
            Text(
              text,
              style: TextStyle(fontSize: 12, color: Colors.grey.shade700),
            ),
            const SizedBox(width: 2),
            Text(
              indicator,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedItemsList(List<Item> items) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        final localizations = AppLocalizations.of(context);

        return Card(
          elevation: 1,
          margin: const EdgeInsets.only(bottom: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap:
                () => Navigator.pushNamed(
                  context,
                  '/item-details',
                  arguments: item,
                ),
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 物品图标或缩略图以及类别标签
                  Column(
                    children: [
                      Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              _getCategoryColor(
                                item.category,
                                context,
                              ).withOpacity(0.15),
                              _getCategoryColor(
                                item.category,
                                context,
                              ).withOpacity(0.3),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: _getCategoryColor(
                                item.category,
                                context,
                              ).withOpacity(0.2),
                              blurRadius: 5,
                              spreadRadius: 1,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child:
                            item.imagePath != null
                                ? ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: Image.network(
                                    item.imagePath!,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return const Icon(
                                        Icons.image_not_supported_outlined,
                                        color: Colors.grey,
                                      );
                                    },
                                  ),
                                )
                                : Icon(
                                  _getCategoryIcon(item.category),
                                  size: 30,
                                  color: _getCategoryColor(
                                    item.category,
                                    context,
                                  ),
                                ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          Formatters.getCategoryName(item.category, context),
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.blue.shade700,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(width: 16),

                  // 物品信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 名称和分类
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                item.name,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 8),

                        // 每日成本（重点突出）
                        Text(
                          '${Formatters.formatCurrency(item.dailyCost ?? 0.0)}/${localizations?.day ?? "天"}',
                          style: const TextStyle(
                            color: Colors.amber,
                            fontWeight: FontWeight.bold,
                          ),
                        ),

                        // 总价格（次要重点）
                        Text(
                          Formatters.formatCurrency(item.price),
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),

                        const SizedBox(height: 8),

                        // 日期和状态
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              Text(
                                Formatters.formatDate(item.purchaseDate),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: _getStatusColor(item.status),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  _getStatusText(item.status, localizations),
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // 使用天数
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 10,
                      horizontal: 12,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.purple.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          item.daysUsed.toString(),
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.purple.shade800,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          localizations?.daysUsed ?? "使用天数",
                          style: TextStyle(color: Colors.white, fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'electronics':
        return Icons.devices;
      case 'mobile':
        return Icons.smartphone;
      case 'computer':
        return Icons.computer;
      case 'household':
        return Icons.chair;
      case 'kitchen':
        return Icons.kitchen;
      case 'books':
        return Icons.book;
      case 'clothing':
        return Icons.checkroom;
      case 'sports':
        return Icons.sports_basketball;
      case 'toys':
        return Icons.toys;
      case 'office':
        return Icons.work;
      default:
        return Icons.category;
    }
  }

  Color _getCategoryColor(String category, BuildContext context) {
    switch (category) {
      case 'electronics':
        return const Color(0xFF2196F3); // 蓝色
      case 'mobile':
        return const Color(0xFF03A9F4); // 浅蓝色
      case 'computer':
        return const Color(0xFF1565C0); // 深蓝色
      case 'household':
        return const Color(0xFF4CAF50); // 绿色
      case 'kitchen':
        return const Color(0xFFFF9800); // 橙色
      case 'books':
        return const Color(0xFF9C27B0); // 紫色
      case 'clothing':
        return const Color(0xFFE91E63); // 粉色
      case 'sports':
        return const Color(0xFFFF5722); // 深橙色
      case 'toys':
        return const Color(0xFF00BCD4); // 青色
      case 'office':
        return const Color(0xFF795548); // 棕色
      default:
        return const Color(0xFF607D8B); // 蓝灰色
    }
  }

  Color _getStatusColor(ItemStatus status) {
    switch (status) {
      case ItemStatus.active:
        return Colors.green;
      case ItemStatus.damaged:
        return Colors.orange;
      case ItemStatus.inactive:
        return Colors.grey;
      case ItemStatus.transfer:
        return Colors.blue;
      default:
        return Colors.blue;
    }
  }

  String _getStatusText(ItemStatus status, AppLocalizations? localizations) {
    switch (status) {
      case ItemStatus.active:
        return localizations?.statusActive ?? '使用中';
      case ItemStatus.damaged:
        return localizations?.statusDamaged ?? '已损坏';
      case ItemStatus.inactive:
        return localizations?.statusInactive ?? '已停用';
      case ItemStatus.transfer:
        return localizations?.statusTransfer ?? '已转让';
      default:
        return '';
    }
  }

  void _filterByStatus(BuildContext context, String value) {
    final provider = Provider.of<BaseItemProvider>(context, listen: false);

    if (value == 'all') {
      provider.loadItems();
    } else if (value == 'active') {
      provider.loadActiveItems();
    }
  }

  Widget _buildCategoryTab(Map<String, List<Item>> categorizedItems) {
    final categories = categorizedItems.keys.toList();
    final localizations = AppLocalizations.of(context);

    if (categories.isEmpty) {
      return Center(child: Text(localizations?.noData ?? '没有分类数据'));
    }

    return RefreshIndicator(
      onRefresh: () async {
        // 下拉刷新时重新加载数据
        await Provider.of<BaseItemProvider>(context, listen: false).loadItems();
        debugPrint('分类页面数据已刷新');
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final items = categorizedItems[category]!;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ListTile(
                title: Text(
                  category,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                trailing: Text('${items.length}个'),
              ),
              _buildItemsList(items),
              const SizedBox(height: 8),
              const Divider(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildItemsList(List<Item> items) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: items.length,
      itemBuilder: (context, index) {
        return ItemCard(
          item: items[index],
          onTap:
              () => Navigator.pushNamed(
                context,
                '/item-details',
                arguments: items[index],
              ),
        );
      },
    );
  }

  // 搜索和筛选逻辑
  void _filterItems() {
    if (_searchQuery.isEmpty) {
      setState(() {
        // 如果只有分类筛选，保留分类筛选状态
        if (!_isCategoryFiltered) {
          _isFiltered = false;
          _filteredItems = [];
        }
      });
    } else {
      // 更新搜索状态
      setState(() {});
    }
  }

  void _showSortOptions(BuildContext context) {
    final provider = Provider.of<BaseItemProvider>(context, listen: false);
    final localizations = AppLocalizations.of(context);

    showModalBottomSheet(
      context: context,
      builder:
          (context) => SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  title: Text(localizations?.itemName ?? 'Sort by name'),
                  leading: const Icon(Icons.sort_by_alpha),
                  onTap: () {
                    // 排序逻辑在UI层实现
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  title: Text(localizations?.purchaseDate ?? 'Sort by date'),
                  leading: const Icon(Icons.calendar_today),
                  onTap: () {
                    // 排序逻辑在UI层实现
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  title: Text(localizations?.purchasePrice ?? 'Sort by price'),
                  leading: const Icon(Icons.attach_money),
                  onTap: () {
                    // 排序逻辑在UI层实现
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  title: Text(localizations?.dailyCost ?? 'Sort by daily cost'),
                  leading: const Icon(Icons.trending_down),
                  onTap: () {
                    // 排序逻辑在UI层实现
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
    );
  }

  void _showCategoryFilter(BuildContext context) {
    final provider = Provider.of<BaseItemProvider>(context, listen: false);
    final localizations = AppLocalizations.of(context);
    final categories = [
      'all',
      'electronics',
      'mobile',
      'computer',
      'household',
      'kitchen',
      'books',
      'clothing',
      'sports',
      'toys',
      'office',
      'others',
    ];

    showModalBottomSheet(
      context: context,
      builder:
          (context) => SafeArea(
            child: ListView(
              shrinkWrap: true,
              children: [
                ...categories.map(
                  (category) => ListTile(
                    title: Text(
                      category == 'all'
                          ? (localizations?.showAllItems ?? 'All categories')
                          : Formatters.getCategoryName(category, context),
                    ),
                    leading: Icon(_getCategoryIcon(category)),
                    onTap: () {
                      if (category == 'all') {
                        setState(() {
                          _isCategoryFiltered = false;
                          _selectedCategory = '';
                          _isFiltered = false;
                          _filteredItems = [];
                        });
                      } else {
                        // 本地筛选指定类别的物品
                        final filteredItems =
                            provider.items
                                .where(
                                  (item) =>
                                      item.category.toLowerCase() ==
                                      category.toLowerCase(),
                                )
                                .toList();

                        print('筛选出 ${filteredItems.length} 个 $category 类别的物品');

                        setState(() {
                          _filteredItems = filteredItems;
                          _isFiltered = true;
                          _isCategoryFiltered = true;
                          _selectedCategory = category;
                        });
                      }
                      Navigator.pop(context);
                    },
                  ),
                ),
              ],
            ),
          ),
    );
  }

  void _showStatusFilter(BuildContext context) {
    final provider = Provider.of<BaseItemProvider>(context, listen: false);
    final localizations = AppLocalizations.of(context);

    showModalBottomSheet(
      context: context,
      builder:
          (context) => SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  title: Text(localizations?.showAllItems ?? 'All statuses'),
                  leading: const Icon(Icons.all_inclusive),
                  onTap: () {
                    setState(() {
                      // 只清除状态筛选，保留分类筛选
                      if (_isCategoryFiltered) {
                        // 重新应用分类筛选
                        final filteredItems =
                            provider.items
                                .where(
                                  (item) =>
                                      item.category.toLowerCase() ==
                                      _selectedCategory.toLowerCase(),
                                )
                                .toList();
                        _filteredItems = filteredItems;
                        _isFiltered = true;
                      } else {
                        _isFiltered = false;
                        _filteredItems = [];
                      }
                    });
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  title: Text(localizations?.statusActive ?? 'Active'),
                  leading: Icon(Icons.check_circle, color: Colors.green),
                  onTap: () {
                    final filteredItems =
                        provider.items
                            .where((item) => item.status == ItemStatus.active)
                            .toList();

                    setState(() {
                      _filteredItems = filteredItems;
                      _isFiltered = true;
                    });

                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  title: Text(localizations?.statusDamaged ?? 'Damaged'),
                  leading: Icon(Icons.warning, color: Colors.orange),
                  onTap: () {
                    // 在UI层面直接筛选损坏状态的物品
                    final items = provider.items;
                    final filteredItems =
                        items
                            .where((item) => item.status == ItemStatus.damaged)
                            .toList();

                    print(
                      '筛选出 ${filteredItems.length} 个损坏物品，状态码：${ItemStatus.damaged.index}',
                    );

                    setState(() {
                      _filteredItems = filteredItems;
                      _isFiltered = true;
                    });

                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  title: Text(localizations?.statusInactive ?? 'Inactive'),
                  leading: Icon(Icons.cancel, color: Colors.red),
                  onTap: () {
                    // 在UI层面直接筛选停用状态的物品
                    final items = provider.items;
                    final filteredItems =
                        items
                            .where((item) => item.status == ItemStatus.inactive)
                            .toList();

                    print(
                      '筛选出 ${filteredItems.length} 个停用物品，状态码：${ItemStatus.inactive.index}',
                    );

                    setState(() {
                      _filteredItems = filteredItems;
                      _isFiltered = true;
                    });

                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  title: Text(localizations?.statusTransfer ?? 'Transferred'),
                  leading: Icon(Icons.transform, color: Colors.blue),
                  onTap: () {
                    // 在UI层面直接筛选转让状态的物品
                    final items = provider.items;
                    final filteredItems =
                        items
                            .where((item) => item.status == ItemStatus.transfer)
                            .toList();

                    print(
                      '筛选出 ${filteredItems.length} 个转让物品，状态码：${ItemStatus.transfer.index}',
                    );

                    setState(() {
                      _filteredItems = filteredItems;
                      _isFiltered = true;
                    });

                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
    );
  }

  // 物品搜索过滤
  List<Item> _getFilteredItems(List<Item> items) {
    // 已应用筛选
    if (_isFiltered) {
      // 如果同时有搜索查询,在筛选结果上继续搜索
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return _filteredItems
            .where(
              (item) =>
                  item.name.toLowerCase().contains(query) ||
                  item.category.toLowerCase().contains(query) ||
                  (item.notes?.toLowerCase().contains(query) ?? false),
            )
            .toList();
      }
      return _filteredItems;
    }

    // 只应用分类筛选
    if (_isCategoryFiltered && _selectedCategory.isNotEmpty) {
      final category = _selectedCategory.toLowerCase();
      final filteredItems =
          items
              .where((item) => item.category.toLowerCase() == category)
              .toList();

      // 如果同时有搜索查询
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return filteredItems
            .where(
              (item) =>
                  item.name.toLowerCase().contains(query) ||
                  (item.notes?.toLowerCase().contains(query) ?? false),
            )
            .toList();
      }

      return filteredItems;
    }

    // 只有搜索查询
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      return items
          .where(
            (item) =>
                item.name.toLowerCase().contains(query) ||
                item.category.toLowerCase().contains(query) ||
                (item.notes?.toLowerCase().contains(query) ?? false),
          )
          .toList();
    }

    // 没有任何筛选
    return items;
  }
}
