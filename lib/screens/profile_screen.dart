import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:developer' as developer;
import 'package:calculate_cost_flutter/l10n/app_localizations.dart';

import '../models/item.dart';
import '../models/user.dart';

import '../services/database_service.dart';
import '../services/api_service.dart';
import '../services/api_auth_service.dart';
import '../services/base_auth_service.dart';
import '../providers/base_item_provider.dart';
import '../providers/app_settings_provider.dart';
import '../utils/constants.dart';
import 'login_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  _ProfileScreenState createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  // 用于控制物品数量刷新
  final ValueNotifier<bool> _refreshTrigger = ValueNotifier<bool>(false);
  bool _hasRefreshedProfile = false;

  @override
  void dispose() {
    _refreshTrigger.dispose();
    super.dispose();
  }

  // 格式化日期函数
  String _formatDate(DateTime date) {
    // 转换为本地时区
    final localDate = date.toLocal();
    return "${localDate.year}-${localDate.month.toString().padLeft(2, '0')}-${localDate.day.toString().padLeft(2, '0')} ${localDate.hour.toString().padLeft(2, '0')}:${localDate.minute.toString().padLeft(2, '0')}";
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<BaseAuthService>(context);

    // 只在第一次构建时刷新用户资料，避免无限循环请求
    if (!_hasRefreshedProfile) {
      _hasRefreshedProfile = true; // 标记已刷新，避免重复刷新

      // 使用microtask避免在build中直接调用异步方法
      Future.microtask(() async {
        await authService.refreshUserProfile();
        // 使用setState触发一次重建，确保UI更新
        if (mounted) setState(() {});
      });
    }

    final isLoggedIn = authService.isLoggedIn;
    final user = authService.currentUser;
    final isPremium = authService.isPremium && !authService.isMembershipExpired;
    final localizations = AppLocalizations.of(context);

    // 记录当前会员状态信息
    developer.log(
      'Profile页面: 用户=${user?.displayName}, '
      '会员状态=${user?.userType == UserType.premium}, '
      '到期日期=${user?.membershipExpiryDate}, '
      'isPremium计算结果=$isPremium',
      name: 'ProfileScreen',
    );

    return Scaffold(
      body: SafeArea(
        child: ListView(
          children: [
            // 顶部用户信息区域
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Theme.of(context).colorScheme.primary.withOpacity(0.2),
                    Theme.of(context).colorScheme.secondary.withOpacity(0.3),
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.02),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // 用户头像
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors:
                            isPremium
                                ? [
                                  Colors.amber.shade200,
                                  Colors.orange.shade300,
                                ]
                                : [Colors.white, Colors.white70],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.06),
                          blurRadius: 6,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Center(
                      child:
                          isLoggedIn
                              ? Text(
                                user?.displayName
                                        ?.substring(0, 1)
                                        .toUpperCase() ??
                                    "U",
                                style: TextStyle(
                                  fontSize: 32,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      isPremium
                                          ? Colors.white
                                          : Theme.of(
                                            context,
                                          ).colorScheme.primary,
                                ),
                              )
                              : Icon(
                                Icons.person,
                                size: 40,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                    ),
                  ),
                  const SizedBox(width: 20),
                  // 用户信息或登录按钮
                  Expanded(
                    child:
                        isLoggedIn
                            ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  user?.displayName ??
                                      localizations?.profile ??
                                      '用户',
                                  style: TextStyle(
                                    fontSize: 22,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey[700],
                                  ),
                                ),
                                const SizedBox(height: 6),
                                Text(
                                  user?.email ?? '',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 14,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                isPremium
                                    ? Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 10,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.amber.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(20),
                                        border: Border.all(
                                          color: Colors.amber.shade300,
                                          width: 1,
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          const Icon(
                                            Icons.star,
                                            color: Colors.amber,
                                            size: 16,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            localizations?.premiumUser ??
                                                'Premium User',
                                            style: TextStyle(
                                              color: Colors.amber.shade800,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                    : const SizedBox.shrink(),
                                const SizedBox(height: 4),
                                // 添加会员到期时间显示
                                if (isPremium &&
                                    user?.membershipExpiryDate != null)
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 10,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.amber.withOpacity(0.05),
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                        color: Colors.amber.withOpacity(0.2),
                                        width: 1,
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.access_time,
                                          color: Colors.amber[600],
                                          size: 14,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          '${localizations?.expiryDate ?? "Expires on"}: ${_formatDate(user!.membershipExpiryDate!)}',
                                          style: TextStyle(
                                            color: Colors.amber[700],
                                            fontSize: 11,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                if (!isPremium)
                                  ValueListenableBuilder<bool>(
                                    valueListenable: _refreshTrigger,
                                    builder: (context, _, __) {
                                      // 直接从用户对象获取物品配额信息，而不是发起网络请求
                                      final authService =
                                          Provider.of<BaseAuthService>(
                                            context,
                                            listen: false,
                                          );

                                      // 获取物品提供器以访问已有物品数量
                                      final itemProvider =
                                          Provider.of<BaseItemProvider>(
                                            context,
                                            listen: false,
                                          );

                                      // 计算已使用数量和剩余数量
                                      final int totalLimit = 30; // 免费用户物品限制为30
                                      final int used =
                                          itemProvider.items.length;
                                      final int remaining = totalLimit - used;

                                      return Row(
                                        children: [
                                          Text(
                                            localizations != null
                                                ? localizations.avaliableItems(
                                                  remaining,
                                                )
                                                : '可添加物品：$remaining/$totalLimit',
                                            style: TextStyle(
                                              color: Colors.grey[700],
                                              fontSize: 12,
                                            ),
                                          ),
                                          const SizedBox(width: 4),
                                          InkWell(
                                            onTap: () {
                                              // 刷新用户资料而不是物品列表
                                              // 使用强制刷新方法
                                              authService
                                                  .forceRefreshUserProfile()
                                                  .then((_) {
                                                    if (mounted) {
                                                      setState(() {
                                                        // 强制页面刷新
                                                      });
                                                    }
                                                  });

                                              // 刷新触发器
                                              _refreshTrigger.value =
                                                  !_refreshTrigger.value;
                                              ScaffoldMessenger.of(
                                                context,
                                              ).showSnackBar(
                                                SnackBar(
                                                  content: Text(
                                                    localizations
                                                            ?.quotaRefreshed ??
                                                        '已刷新个人信息',
                                                  ),
                                                  duration: const Duration(
                                                    seconds: 1,
                                                  ),
                                                ),
                                              );
                                            },
                                            child: Icon(
                                              Icons.refresh,
                                              size: 14,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                        ],
                                      );
                                    },
                                  ),
                              ],
                            )
                            : ElevatedButton(
                              onPressed: () {
                                Navigator.pushNamed(context, AppRoutes.login);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.white,
                                foregroundColor:
                                    Theme.of(context).colorScheme.primary,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 12,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20),
                                ),
                              ),
                              child: Text(
                                localizations?.login ?? 'Login / Register',
                              ),
                            ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // 功能列表
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  children: [
                    // 会员中心
                    if (isLoggedIn)
                      _buildProfileMenuItem(
                        context,
                        icon: Icons.card_membership,
                        title: localizations?.membership ?? 'Membership',
                        onTap: () {
                          Navigator.pushNamed(context, AppRoutes.membership);
                        },
                      ),

                    // 备份与恢复
                    if (isLoggedIn)
                      _buildProfileMenuItem(
                        context,
                        icon: Icons.backup,
                        title:
                            localizations?.backupAndRestore ??
                            'Backup & Restore',
                        onTap: () => _showDataBackupDialog(context),
                      ),

                    // 意见反馈
                    if (isLoggedIn)
                      _buildProfileMenuItem(
                        context,
                        icon: Icons.feedback,
                        title: localizations?.feedback ?? 'Feedback',
                        onTap: () {
                          Navigator.pushNamed(context, AppRoutes.feedback);
                        },
                      ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 设置列表
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 16,
                        top: 16,
                        bottom: 8,
                      ),
                      child: Text(
                        localizations?.settings ?? 'Settings',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ),
                    // 主题设置
                    _buildProfileMenuItem(
                      context,
                      icon: Icons.palette,
                      title: localizations?.theme ?? 'Theme',
                      trailing: Consumer<AppSettingsProvider>(
                        builder: (context, settings, child) {
                          return Text(
                            _getLocalizedThemeName(context, settings.themeMode),
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 14,
                            ),
                          );
                        },
                      ),
                      onTap: () => _showThemeSettingsDialog(context),
                    ),

                    // 语言设置
                    _buildProfileMenuItem(
                      context,
                      icon: Icons.language,
                      title: localizations?.language ?? 'Language',
                      trailing: Consumer<AppSettingsProvider>(
                        builder: (context, settings, child) {
                          return Text(
                            _getLocalizedLanguageName(context, settings.locale),
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 14,
                            ),
                          );
                        },
                      ),
                      onTap: () => _showLanguageSettingsDialog(context),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 40),

            // 登录用户显示退出登录按钮
            if (isLoggedIn)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: ElevatedButton.icon(
                  onPressed: () => _showLogoutConfirmDialog(context),
                  icon: const Icon(Icons.logout),
                  label: Text(localizations?.logout ?? 'Logout'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(
                      context,
                    ).colorScheme.primary.withOpacity(0.1),
                    foregroundColor: Theme.of(context).colorScheme.primary,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                ),
              ),

            // 非登录用户显示登录按钮
            if (!isLoggedIn)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: _buildProfileMenuItem(
                    context,
                    icon: Icons.login,
                    title: localizations?.login ?? 'Login',
                    onTap: () {
                      Navigator.pushNamed(context, AppRoutes.login);
                    },
                  ),
                ),
              ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // 构建个人资料菜单项
  Widget _buildProfileMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    Widget? trailing,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Icon(icon, color: Theme.of(context).colorScheme.primary),
      ),
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
      trailing: trailing ?? const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  // 获取主题模式的本地化名称
  String _getLocalizedThemeName(BuildContext context, AppThemeMode themeMode) {
    final localizations = AppLocalizations.of(context);
    switch (themeMode) {
      case AppThemeMode.light:
        return localizations?.lightMode ?? '浅色模式';
      case AppThemeMode.dark:
        return localizations?.darkMode ?? '深色模式';
      case AppThemeMode.system:
        return localizations?.systemMode ?? '跟随系统';
    }
  }

  // 获取语言的本地化名称
  String _getLocalizedLanguageName(BuildContext context, Locale? locale) {
    final localizations = AppLocalizations.of(context);
    if (locale == null) {
      return localizations?.followSystem ?? '跟随系统';
    } else if (locale.languageCode == 'en') {
      return localizations?.english ?? '英文';
    } else if (locale.languageCode == 'zh') {
      return localizations?.chinese ?? '中文';
    } else {
      return locale.languageCode;
    }
  }

  // 显示需要登录的提示对话框
  void _showRequireLoginDialog(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(localizations?.loginRequired ?? '需要登录'),
            content: Text(localizations?.pleaseLoginFirst ?? '请先登录您的账号以使用此功能'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(localizations?.cancel ?? '稍后再说'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(context, AppRoutes.login);
                },
                child: Text(localizations?.login ?? '去登录'),
              ),
            ],
          ),
    );
  }

  // 显示退出登录确认对话框
  void _showLogoutConfirmDialog(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(localizations?.logout ?? '退出登录'),
            content: Text(localizations?.logoutConfirm ?? '您确定要退出登录吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(localizations?.cancel ?? '取消'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // 执行退出登录
                  Provider.of<BaseAuthService>(context, listen: false).logout();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(localizations?.loggedOut ?? '已退出登录'),
                    ),
                  );
                },
                child: Text(localizations?.ok ?? '确定'),
              ),
            ],
          ),
    );
  }

  // 显示数据备份对话框
  void _showDataBackupDialog(BuildContext context) async {
    final authService = Provider.of<BaseAuthService>(context, listen: false);

    // 先刷新用户信息以获取最新会员状态
    // 使用强制刷新方法确保获取最新会员状态
    await authService.forceRefreshUserProfile();
    developer.log(
      '强制刷新用户资料完成：会员状态=${authService.isPremium}, 过期状态=${authService.isMembershipExpired}',
      name: 'ProfileScreen',
    );

    // 刷新后重新获取会员状态
    final isPremium = authService.isPremium && !authService.isMembershipExpired;
    if (!mounted) return;
    final localizations = AppLocalizations.of(context);

    if (!isPremium) {
      // 非会员用户显示升级提示
      showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: Text(localizations?.premiumExclusive ?? '会员专属功能'),
              content: Text(
                localizations?.upgradeToUsePremiumDesc ??
                    '备份与恢复功能仅对会员用户开放。升级为会员即可使用云端备份与恢复功能，让您的数据更安全。',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(localizations?.cancel ?? '稍后再说'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    Navigator.pushNamed(context, AppRoutes.membership);
                  },
                  child: Text(localizations?.membership ?? '升级会员'),
                ),
              ],
            ),
      );
    } else {
      // 会员用户正常显示备份恢复对话框
      showDialog(context: context, builder: (context) => BackupRestoreDialog());
    }
  }

  // 显示主题设置对话框
  void _showThemeSettingsDialog(BuildContext context) {
    final settingsProvider = Provider.of<AppSettingsProvider>(
      context,
      listen: false,
    );
    final localizations = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(localizations?.theme ?? '主题设置'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  title: Text(localizations?.lightMode ?? '浅色模式'),
                  leading: Radio<AppThemeMode>(
                    value: AppThemeMode.light,
                    groupValue: settingsProvider.themeMode,
                    onChanged: (value) {
                      settingsProvider.setThemeMode(AppThemeMode.light);
                      Navigator.pop(context);
                    },
                  ),
                ),
                ListTile(
                  title: Text(localizations?.darkMode ?? '深色模式'),
                  leading: Radio<AppThemeMode>(
                    value: AppThemeMode.dark,
                    groupValue: settingsProvider.themeMode,
                    onChanged: (value) {
                      settingsProvider.setThemeMode(AppThemeMode.dark);
                      Navigator.pop(context);
                    },
                  ),
                ),
                ListTile(
                  title: Text(localizations?.systemMode ?? '跟随系统'),
                  leading: Radio<AppThemeMode>(
                    value: AppThemeMode.system,
                    groupValue: settingsProvider.themeMode,
                    onChanged: (value) {
                      settingsProvider.setThemeMode(AppThemeMode.system);
                      Navigator.pop(context);
                    },
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(localizations?.cancel ?? '关闭'),
              ),
            ],
          ),
    );
  }

  // 显示语言设置对话框
  void _showLanguageSettingsDialog(BuildContext context) {
    final settingsProvider = Provider.of<AppSettingsProvider>(
      context,
      listen: false,
    );
    final localizations = AppLocalizations.of(context);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(localizations?.language ?? '语言设置'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  title: Text(localizations?.english ?? '英文'),
                  subtitle: const Text('English'),
                  leading: Radio<String>(
                    value: 'en',
                    groupValue:
                        settingsProvider.locale?.languageCode ?? 'system',
                    onChanged: (value) {
                      settingsProvider.useEnglish();
                      Navigator.pop(context);
                    },
                  ),
                ),
                ListTile(
                  title: Text(localizations?.chinese ?? '中文'),
                  subtitle: const Text('简体中文'),
                  leading: Radio<String>(
                    value: 'zh',
                    groupValue:
                        settingsProvider.locale?.languageCode ?? 'system',
                    onChanged: (value) {
                      settingsProvider.useChinese();
                      Navigator.pop(context);
                    },
                  ),
                ),
                ListTile(
                  title: Text(localizations?.followSystem ?? '跟随系统'),
                  subtitle: const Text('使用设备语言设置'),
                  leading: Radio<String>(
                    value: 'system',
                    groupValue:
                        settingsProvider.locale?.languageCode ?? 'system',
                    onChanged: (value) {
                      settingsProvider.useSystemLocale();
                      Navigator.pop(context);
                    },
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(localizations?.cancel ?? '关闭'),
              ),
            ],
          ),
    );
  }
}

// 备份与恢复对话框
class BackupRestoreDialog extends StatefulWidget {
  const BackupRestoreDialog({super.key});

  @override
  _BackupRestoreDialogState createState() => _BackupRestoreDialogState();
}

class _BackupRestoreDialogState extends State<BackupRestoreDialog> {
  bool _isBackingUp = false;
  bool _isRestoring = false;
  String? _statusMessage;
  bool _isSuccess = false;

  // 备份数据
  Future<void> _backupData() async {
    final localizations = AppLocalizations.of(context);

    setState(() {
      _isBackingUp = true;
      _statusMessage = null;
    });

    try {
      // 获取ItemProvider
      final itemProvider = Provider.of<BaseItemProvider>(
        context,
        listen: false,
      );
      final apiService = ApiService();

      // 强制加载所有物品（包括停用的）
      await itemProvider.loadItems();
      print('调试-BackupDialog: 强制重新加载所有物品，确保包含已停用物品');

      // 检查网络连接
      final isConnected = await apiService.isNetworkConnected();
      if (!isConnected) {
        setState(() {
          _statusMessage =
              localizations?.networkUnavailable ?? '网络连接不可用，请检查网络设置';
          _isSuccess = false;
          _isBackingUp = false;
        });
        return;
      }

      // 获取当前用户ID
      final authService = Provider.of<BaseAuthService>(context, listen: false);
      final currentUser = authService.currentUser;
      if (currentUser == null || currentUser.id == null) {
        setState(() {
          _statusMessage = localizations?.userInfoError ?? '无法获取用户信息，请重新登录';
          _isSuccess = false;
          _isBackingUp = false;
        });
        return;
      }

      // 获取所有本地物品
      final localItems = itemProvider.items;
      if (localItems.isEmpty) {
        setState(() {
          _statusMessage = localizations?.noItemsToBackup ?? '没有物品需要备份';
          _isSuccess = true;
          _isBackingUp = false;
        });
        return;
      }

      setState(() {
        _statusMessage = localizations?.gettingCloudData ?? '正在获取云端数据...';
      });

      // 1. 获取云端items
      final userId = currentUser.id!;
      final cloudItemsResult = await apiService.getUserItems(userId);

      if (!cloudItemsResult.isSuccess) {
        setState(() {
          _isBackingUp = false;
          _statusMessage =
              localizations?.getCloudDataFailed != null
                  ? localizations?.getCloudDataFailed(
                    cloudItemsResult.error ?? '',
                  )
                  : '获取云端数据失败: ${cloudItemsResult.error}';
          _isSuccess = false;
        });
        return;
      }

      final cloudItems = cloudItemsResult.data ?? [];

      // 2. 比较本地和云端数据，创建待删除和待新增的列表
      List<int> itemIdsToDelete = [];
      List<Item> itemsToAdd = [];
      List<Item> itemsToUpdate = []; // 新增：需要更新的物品列表

      // 2.1 找出云端存在但本地不需要的物品（需要删除）
      if (cloudItems.isNotEmpty) {
        // 创建本地物品ID的集合，用于快速查找
        final Set<int?> localItemIds =
            localItems
                .where((item) => item.id != null)
                .map((item) => item.id)
                .toSet();

        // 找出云端存在但本地没有的物品ID
        for (final cloudItem in cloudItems) {
          if (cloudItem.id != null && !localItemIds.contains(cloudItem.id)) {
            itemIdsToDelete.add(cloudItem.id!);
          }
        }
      }

      // 2.2 找出需要上传到云端的本地物品
      // 创建云端物品ID的集合，用于快速查找
      final Set<int?> cloudItemIds =
          cloudItems
              .where((item) => item.id != null)
              .map((item) => item.id)
              .toSet();

      // 创建云端物品Map，用于检查物品内容是否有变化
      final Map<int, Item> cloudItemMap = {
        for (var item in cloudItems)
          if (item.id != null) item.id!: item,
      };

      for (final localItem in localItems) {
        if (localItem.id == null || !cloudItemIds.contains(localItem.id)) {
          // 物品在云端不存在，需要新增
          itemsToAdd.add(localItem);
        } else {
          // 物品在云端和本地都存在，检查内容是否有变化
          final cloudItem = cloudItemMap[localItem.id!];
          if (cloudItem != null) {
            // 检查关键属性是否发生变化（状态、价格、日期等）
            if (cloudItem.status != localItem.status ||
                cloudItem.price != localItem.price ||
                cloudItem.name != localItem.name ||
                cloudItem.category != localItem.category ||
                cloudItem.transferDate != localItem.transferDate ||
                cloudItem.purchaseDate != localItem.purchaseDate ||
                cloudItem.sellPrice != localItem.sellPrice) {
              // 物品内容有变化，需要更新
              itemsToUpdate.add(localItem);
              developer.log(
                '物品需要更新: ID=${localItem.id}, 名称=${localItem.name}, 状态=${localItem.status}',
                name: 'BackupDialog',
              );
            }
          }
        }
      }

      // 输出日志
      developer.log(
        '本地物品: ${localItems.length}个, 云端物品: ${cloudItems.length}个',
        name: 'BackupDialog',
      );
      developer.log(
        '待删除: ${itemIdsToDelete.length}个, 待新增: ${itemsToAdd.length}个, 待更新: ${itemsToUpdate.length}个',
        name: 'BackupDialog',
      );

      // 3. 如果有需要删除的物品，先批量删除
      if (itemIdsToDelete.isNotEmpty) {
        setState(() {
          _statusMessage =
              localizations?.deletingCloudItems ?? '正在删除云端不需要的物品...';
        });

        final deleteResult = await apiService.batchDeleteItems(itemIdsToDelete);

        if (!deleteResult.isSuccess) {
          setState(() {
            _isBackingUp = false;
            _statusMessage =
                localizations?.deleteCloudItemsFailed != null
                    ? localizations?.deleteCloudItemsFailed(
                      deleteResult.error ?? '',
                    )
                    : '删除云端物品失败: ${deleteResult.error}';
            _isSuccess = false;
          });
          return;
        }

        developer.log(
          '已删除云端物品: ${deleteResult.data?['deleted_count'] ?? 0}个',
          name: 'BackupDialog',
        );
      }

      // 4. 上传需要新增的物品
      if (itemsToAdd.isNotEmpty) {
        setState(() {
          _statusMessage =
              localizations?.uploadingLocalItems != null
                  ? localizations?.uploadingLocalItems(0, itemsToAdd.length)
                  : '正在上传本地物品到云端 (0/${itemsToAdd.length})...';
        });

        int successCount = 0;
        int failureCount = 0;

        // 逐个上传物品
        for (int i = 0; i < itemsToAdd.length; i++) {
          final item = itemsToAdd[i];

          // 更新进度
          setState(() {
            _statusMessage =
                localizations?.uploadingLocalItems != null
                    ? localizations?.uploadingLocalItems(
                      i + 1,
                      itemsToAdd.length,
                    )
                    : '正在上传本地物品到云端 (${i + 1}/${itemsToAdd.length})...';
          });

          // 使用addItem方法上传单个物品
          final result = await apiService.addItem(item);

          if (result.isSuccess) {
            successCount++;
          } else {
            failureCount++;
            developer.log(
              '上传物品失败: ${result.error}, 物品名称: ${item.name}',
              name: 'BackupDialog',
            );
          }
        }

        developer.log(
          '物品上传完成 - 成功: $successCount, 失败: $failureCount',
          name: 'BackupDialog',
        );

        if (failureCount > 0) {
          setState(() {
            _isBackingUp = false;
            _statusMessage =
                localizations?.partialUploadFailed != null
                    ? localizations?.partialUploadFailed(
                      successCount,
                      failureCount,
                      itemIdsToDelete.length,
                    )
                    : '部分物品上传失败，已同步 $successCount 个物品，失败 $failureCount 个物品，删除 ${itemIdsToDelete.length} 个物品';
            _isSuccess = successCount > 0; // 只要有一部分成功就算部分成功
          });
          return;
        }
      }

      // 5. 更新需要更新的物品
      if (itemsToUpdate.isNotEmpty) {
        setState(() {
          _statusMessage =
              localizations?.updatingItems != null
                  ? localizations?.updatingItems(0, itemsToUpdate.length)
                  : '正在更新物品 (0/${itemsToUpdate.length})...';
        });

        int successCount = 0;
        int failureCount = 0;

        // 逐个更新物品
        for (int i = 0; i < itemsToUpdate.length; i++) {
          final item = itemsToUpdate[i];

          // 更新进度
          setState(() {
            _statusMessage =
                localizations?.updatingItems != null
                    ? localizations?.updatingItems(i + 1, itemsToUpdate.length)
                    : '正在更新物品 (${i + 1}/${itemsToUpdate.length})...';
          });

          // 使用updateItem方法更新单个物品
          final result = await apiService.updateItem(item);

          if (result.isSuccess) {
            successCount++;
          } else {
            failureCount++;
            developer.log(
              '更新物品失败: ${result.error}, 物品名称: ${item.name}',
              name: 'BackupDialog',
            );
          }
        }

        developer.log(
          '物品更新完成 - 成功: $successCount, 失败: $failureCount',
          name: 'BackupDialog',
        );

        if (failureCount > 0 && successCount == 0) {
          setState(() {
            _isBackingUp = false;
            _statusMessage = '所有物品更新失败，请稍后重试';
            _isSuccess = false;
          });
          return;
        }
      }

      setState(() {
        _isBackingUp = false;
        _statusMessage =
            localizations?.backupSuccess != null
                ? localizations?.backupSuccessWithUpdate(
                  itemsToAdd.length,
                  itemIdsToDelete.length,
                  itemsToUpdate.length,
                )
                : '数据备份成功！已同步 ${itemsToAdd.length} 个物品，删除 ${itemIdsToDelete.length} 个物品，更新 ${itemsToUpdate.length} 个物品';
        _isSuccess = true;
      });
    } catch (e) {
      setState(() {
        _isBackingUp = false;
        _statusMessage =
            localizations?.backupError != null
                ? localizations?.backupError(e.toString())
                : '备份过程中发生错误：${e.toString()}';
        _isSuccess = false;
      });
    }
  }

  // 恢复数据
  Future<void> _restoreData() async {
    final localizations = AppLocalizations.of(context);

    setState(() {
      _isRestoring = true;
      _statusMessage = null;
    });

    try {
      // 获取服务实例
      final itemProvider = Provider.of<BaseItemProvider>(
        context,
        listen: false,
      );
      final apiService = ApiService();
      final databaseService = DatabaseService();

      // 检查网络连接
      final isConnected = await apiService.isNetworkConnected();
      if (!isConnected) {
        setState(() {
          _statusMessage =
              localizations?.networkUnavailable ?? '网络连接不可用，请检查网络设置';
          _isSuccess = false;
          _isRestoring = false;
        });
        return;
      }

      // 获取当前用户ID
      final authService = Provider.of<BaseAuthService>(context, listen: false);
      final currentUser = authService.currentUser;
      if (currentUser == null || currentUser.id == null) {
        setState(() {
          _statusMessage = localizations?.userInfoError ?? '无法获取用户信息，请重新登录';
          _isSuccess = false;
          _isRestoring = false;
        });
        return;
      }

      // 1. 从云端拉取用户下的所有items: /items/user/{userId}
      final userId = currentUser.id!;
      final result = await apiService.getUserItems(userId);

      if (result.isSuccess && result.data != null) {
        // 2. 删除本地database中的数据
        await databaseService.clearUserItems(userId);

        // 3. 将云端的数据插入本地的数据库
        for (var item in result.data!) {
          // 确保物品关联到当前用户
          final itemWithUserId = item.copyWith(userId: userId);
          await databaseService.safeInsertItem(itemWithUserId);
        }

        // 刷新物品列表
        await itemProvider.loadItems();

        setState(() {
          _isRestoring = false;
          _statusMessage =
              localizations?.restoreSuccess != null
                  ? localizations?.restoreSuccess(result.data!.length)
                  : '已成功恢复 ${result.data!.length} 个物品';
          _isSuccess = true;
        });
      } else {
        setState(() {
          _isRestoring = false;
          _statusMessage =
              localizations?.restoreFailed != null
                  ? localizations?.restoreFailed(result.error ?? '')
                  : '恢复失败：${result.error}';
          _isSuccess = false;
        });
      }
    } catch (e) {
      setState(() {
        _isRestoring = false;
        _statusMessage =
            localizations?.restoreError != null
                ? localizations?.restoreError(e.toString())
                : '恢复过程中发生错误：${e.toString()}';
        _isSuccess = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return AlertDialog(
      title: Text(localizations?.backupRestoreTitle ?? '数据备份与恢复'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localizations?.backupRestoreDescription ??
                  '您可以将物品数据备份到云端，或从云端恢复您的数据。',
            ),
            const SizedBox(height: 16),

            // 备份按钮
            ElevatedButton.icon(
              onPressed: _isBackingUp || _isRestoring ? null : _backupData,
              icon:
                  _isBackingUp
                      ? Container(
                        width: 24,
                        height: 24,
                        padding: const EdgeInsets.all(2.0),
                        child: const CircularProgressIndicator(strokeWidth: 3),
                      )
                      : const Icon(Icons.backup),
              label: Text(localizations?.backupToCloud ?? '备份到云端'),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 45),
              ),
            ),

            const SizedBox(height: 12),

            // 恢复按钮
            ElevatedButton.icon(
              onPressed: _isBackingUp || _isRestoring ? null : _restoreData,
              icon:
                  _isRestoring
                      ? Container(
                        width: 24,
                        height: 24,
                        padding: const EdgeInsets.all(2.0),
                        child: const CircularProgressIndicator(strokeWidth: 3),
                      )
                      : const Icon(Icons.restore),
              label: Text(localizations?.restoreFromCloud ?? '从云端恢复'),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 45),
              ),
            ),

            // 状态消息
            if (_statusMessage != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color:
                      _isSuccess ? Colors.green.shade100 : Colors.red.shade100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    Icon(
                      _isSuccess ? Icons.check_circle : Icons.error,
                      color: _isSuccess ? Colors.green : Colors.red,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _statusMessage!,
                        style: TextStyle(
                          color:
                              _isSuccess
                                  ? Colors.green.shade800
                                  : Colors.red.shade800,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            const SizedBox(height: 8),

            // 提示信息
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.blue.shade800, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      localizations?.backupCaution ?? '备份和恢复将覆盖现有数据，请谨慎操作',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(localizations?.close ?? '关闭'),
        ),
      ],
    );
  }
}
