import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:calculate_cost_flutter/l10n/app_localizations.dart';
import '../services/base_auth_service.dart';
import '../models/feedback.dart';
import '../services/database_service.dart';
import '../widgets/custom_button.dart';
import '../services/api_service.dart';

class FeedbackScreen extends StatefulWidget {
  const FeedbackScreen({super.key});

  @override
  _FeedbackScreenState createState() => _FeedbackScreenState();
}

class _FeedbackScreenState extends State<FeedbackScreen> {
  final _formKey = GlobalKey<FormState>();
  final _contentController = TextEditingController();
  final _contactInfoController = TextEditingController();
  String? _selectedType;
  bool _isLoading = false;
  final _databaseService = DatabaseService();

  @override
  void initState() {
    super.initState();
    // 在initState中设置默认值
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final localizations = AppLocalizations.of(context);
      if (localizations != null) {
        setState(() {
          _selectedType = localizations.featureSuggestion;
        });
      } else {
        setState(() {
          _selectedType = '功能建议';
        });
      }
    });
  }

  @override
  void dispose() {
    _contentController.dispose();
    _contactInfoController.dispose();
    super.dispose();
  }

  // 获取反馈类型列表
  List<String> _getFeedbackTypes() {
    final localizations = AppLocalizations.of(context);
    if (localizations != null) {
      return [
        localizations.featureSuggestion,
        localizations.performanceIssue,
        localizations.userExperience,
        localizations.membershipService,
        localizations.otherIssues,
      ];
    } else {
      return ['功能建议', '性能问题', '使用体验', '会员服务', '其他问题'];
    }
  }

  // 提交反馈
  Future<void> _submitFeedback() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<BaseAuthService>(context, listen: false);
      final currentUser = authService.currentUser;
      final localizations = AppLocalizations.of(context);

      // 创建反馈对象
      final feedback = UserFeedback(
        userId: currentUser?.id,
        content: _contentController.text.trim(),
        type: _selectedType ?? '功能建议',
        contactInfo: _contactInfoController.text.trim(),
        createdAt: DateTime.now(),
      );

      // 调用API服务发送反馈
      final apiService = ApiService();
      final result = await apiService.submitFeedback(feedback);

      // 同时保存到本地数据库
      await _databaseService.insertFeedback(feedback);

      if (mounted) {
        if (result.isSuccess) {
          // 显示成功消息
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                localizations?.feedbackSuccess ?? '感谢您的反馈，我们将尽快处理！',
              ),
              backgroundColor: Colors.green,
            ),
          );

          // 清空表单
          _contentController.clear();
          _contactInfoController.clear();
          final defaultType = localizations?.featureSuggestion ?? '功能建议';
          setState(() {
            _selectedType = defaultType;
          });
        } else {
          // 显示API错误
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('提交反馈失败: ${result.error}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        // 显示错误消息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('提交反馈失败: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<BaseAuthService>(context);
    final isLoggedIn = authService.isLoggedIn;
    final localizations = AppLocalizations.of(context);
    final feedbackTypes = _getFeedbackTypes();

    return Scaffold(
      appBar: AppBar(title: Text(localizations?.userFeedback ?? '用户反馈')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 页面说明
              Text(
                localizations?.feedbackTip ?? '我们非常重视您的反馈意见，请告诉我们您的想法或遇到的问题：',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 24),

              // 反馈类型选择器
              Text(
                localizations?.feedbackType ?? '反馈类型',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 15,
                  ),
                ),
                value: _selectedType,
                items:
                    feedbackTypes.map((type) {
                      return DropdownMenuItem<String>(
                        value: type,
                        child: Text(type),
                      );
                    }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),

              // 反馈内容输入框
              Text(
                localizations?.feedbackContent ?? '反馈内容',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _contentController,
                decoration: InputDecoration(
                  hintText:
                      localizations?.feedbackContentHint ?? '请详细描述您的建议或问题...',
                  border: const OutlineInputBorder(),
                  contentPadding: const EdgeInsets.all(12),
                ),
                maxLines: 5,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please input feedback';
                  }
                  if (value.length < 10) {
                    return 'Feedback content character must more than 10';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // 联系方式输入框（可选）
              Text(
                localizations?.contactInfo ?? '联系方式（可选）',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _contactInfoController,
                decoration: InputDecoration(
                  hintText:
                      localizations?.contactInfoHint ?? '您可以留下电子邮件或手机号，以便我们回复您',
                  border: const OutlineInputBorder(),
                  contentPadding: const EdgeInsets.all(12),
                ),
                keyboardType: TextInputType.emailAddress,
              ),
              const SizedBox(height: 24),

              // 提交按钮
              CustomButton(
                text: localizations?.submitFeedback ?? '提交反馈',
                onPressed: _isLoading ? null : _submitFeedback,
                isLoading: _isLoading,
                icon: Icons.send,
              ),

              const SizedBox(height: 16),

              // 用户须知
              if (!isLoggedIn)
                Card(
                  color: Colors.amber,
                  child: Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: Row(
                      children: [
                        const Icon(Icons.info_outline, color: Colors.black87),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            localizations?.anonymousFeedbackTip ??
                                '您当前是匿名反馈。登录账号后，我们能更好地处理您的反馈。',
                            style: const TextStyle(color: Colors.black87),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
