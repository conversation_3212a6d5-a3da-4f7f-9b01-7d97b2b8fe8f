import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:calculate_cost_flutter/l10n/app_localizations.dart';
import '../providers/base_item_provider.dart';
import '../models/item.dart';
import '../utils/constants.dart';
import '../utils/formatters.dart';

class AddItemScreen extends StatefulWidget {
  final Item? itemToEdit;

  const AddItemScreen({super.key, this.itemToEdit});

  @override
  State<AddItemScreen> createState() => _AddItemScreenState();
}

class _AddItemScreenState extends State<AddItemScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _priceController = TextEditingController();
  final _notesController = TextEditingController();
  String _selectedCategory = ItemCategories.categories.first; // 确保使用列表中的第一个值
  DateTime _purchaseDate = DateTime.now();

  bool _isEditing = false;

  @override
  void initState() {
    super.initState();

    if (widget.itemToEdit != null) {
      // 如果是编辑模式，则填充表单
      _nameController.text = widget.itemToEdit!.name;
      _priceController.text = widget.itemToEdit!.price.toString();
      _selectedCategory = _getCorrectedCategory(widget.itemToEdit!.category);
      _purchaseDate = widget.itemToEdit!.purchaseDate;

      if (widget.itemToEdit!.notes != null) {
        _notesController.text = widget.itemToEdit!.notes!;
      }

      _isEditing = true;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _priceController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isEditing
              ? localizations?.editItemTitle ?? '编辑物品'
              : localizations?.addItemTitle ?? '添加物品',
        ),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildImagePicker(),
              const SizedBox(height: 24),

              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: localizations?.itemName ?? '物品名称',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.inventory),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return localizations?.pleaseEnterItemName ?? '请输入物品名称';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              TextFormField(
                controller: _priceController,
                decoration: InputDecoration(
                  labelText: localizations?.purchasePrice ?? '价格',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.attach_money),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return localizations?.pleaseEnterPrice ?? '请输入价格';
                  }
                  if (double.tryParse(value) == null) {
                    return localizations?.pleaseEnterValidPrice ?? '请输入有效的价格';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              _buildCategoryDropdown(),
              const SizedBox(height: 16),

              _buildDatePicker(),
              const SizedBox(height: 16),

              TextFormField(
                controller: _notesController,
                decoration: InputDecoration(
                  labelText: localizations?.itemNotes ?? '备注',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.note),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 24),

              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _saveItem,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: Text(
                    _isEditing
                        ? localizations?.updateItem ?? '更新物品'
                        : localizations?.saveItem ?? '添加物品',
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImagePicker() {
    final localizations = AppLocalizations.of(context);

    // 获取分类对应的颜色和图标
    final categoryColor =
        _selectedCategory != null
            ? ItemCategories.categoryColors[_selectedCategory] ??
                Theme.of(context).colorScheme.primary
            : Colors.grey;

    final categoryIcon =
        _selectedCategory != null
            ? ItemCategories.categoryIcons[_selectedCategory] ??
                Icons.help_outline
            : Icons.category;

    return Center(
      child: Column(
        children: [
          Container(
            width: 160,
            height: 160,
            decoration: BoxDecoration(
              color: categoryColor.withOpacity(0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(categoryIcon, size: 80, color: categoryColor),
                const SizedBox(height: 8),
                Text(
                  _selectedCategory != null
                      ? Formatters.getCategoryName(_selectedCategory, context)
                      : localizations?.categoryTitle ?? '分类',
                  style: TextStyle(
                    color: categoryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryDropdown() {
    final localizations = AppLocalizations.of(context);

    // 将分类和它们的本地化显示名称映射
    Map<String, String> categoryDisplayNames = {};
    for (String category in ItemCategories.categories) {
      switch (category) {
        case 'electronics':
          categoryDisplayNames[category] =
              localizations?.categoryElectronics ?? '电子产品';
          break;
        case 'household':
          categoryDisplayNames[category] =
              localizations?.categoryHousehold ?? '家居用品';
          break;
        case 'kitchen':
          categoryDisplayNames[category] =
              localizations?.categoryKitchen ?? '厨房用品';
          break;
        case 'books':
          categoryDisplayNames[category] = localizations?.categoryBooks ?? '书籍';
          break;
        case 'clothing':
          categoryDisplayNames[category] =
              localizations?.categoryClothing ?? '衣物';
          break;
        case 'sports':
          categoryDisplayNames[category] =
              localizations?.categorySports ?? '运动用品';
          break;
        case 'toys':
          categoryDisplayNames[category] = localizations?.categoryToys ?? '玩具';
          break;
        case 'office':
          categoryDisplayNames[category] =
              localizations?.categoryOffice ?? '办公用品';
          break;
        case 'others':
          categoryDisplayNames[category] =
              localizations?.categoryOthers ?? '其它';
          break;
        default:
          categoryDisplayNames[category] = category;
      }
    }

    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: localizations?.itemCategory ?? '分类',
        border: const OutlineInputBorder(),
        prefixIcon: const Icon(Icons.category),
      ),
      value: _selectedCategory,
      items:
          ItemCategories.categories.map((String category) {
            return DropdownMenuItem<String>(
              value: category,
              child: Text(categoryDisplayNames[category] ?? category),
            );
          }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedCategory = value;
          });
        }
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return localizations?.pleaseSelectCategory ?? '请选择分类';
        }
        return null;
      },
    );
  }

  Widget _buildDatePicker() {
    final localizations = AppLocalizations.of(context);

    return GestureDetector(
      onTap: () => _selectDate(context),
      child: AbsorbPointer(
        child: TextFormField(
          decoration: InputDecoration(
            labelText: localizations?.purchaseDate ?? '购买日期',
            border: const OutlineInputBorder(),
            prefixIcon: const Icon(Icons.calendar_today),
          ),
          controller: TextEditingController(
            text:
                '${_purchaseDate.year}-${_purchaseDate.month.toString().padLeft(2, '0')}-${_purchaseDate.day.toString().padLeft(2, '0')}',
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return localizations?.pleaseSelectDate ?? '请选择购买日期';
            }
            return null;
          },
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _purchaseDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );

    if (picked != null && picked != _purchaseDate) {
      setState(() {
        _purchaseDate = picked;
      });
    }
  }

  Future<void> _saveItem() async {
    if (_formKey.currentState!.validate()) {
      try {
        final name = _nameController.text.trim();
        final price = double.parse(_priceController.text.replaceAll(',', '.'));
        final notes = _notesController.text.trim();

        // 创建物品对象
        final Item newItem =
            _isEditing
                ? Item(
                  id: widget.itemToEdit!.id,
                  name: name,
                  price: price,
                  category: _selectedCategory,
                  purchaseDate: _purchaseDate,
                  notes: notes.isEmpty ? null : notes,
                  imagePath: null, // 不再使用图片
                  status: widget.itemToEdit!.status,
                  sellPrice: widget.itemToEdit!.sellPrice,
                  transferDate: widget.itemToEdit!.transferDate,
                )
                : Item(
                  name: name,
                  price: price,
                  category: _selectedCategory,
                  purchaseDate: _purchaseDate,
                  notes: notes.isEmpty ? null : notes,
                  imagePath: null, // 不再使用图片
                );

        final itemProvider = Provider.of<BaseItemProvider>(
          context,
          listen: false,
        );

        if (_isEditing) {
          await itemProvider.updateItem(newItem);
        } else {
          await itemProvider.addItem(newItem);
        }

        // 操作成功
        if (mounted) {
          Navigator.of(context).pop();
        }
      } catch (e) {
        // 显示错误信息
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('保存失败: $e')));
        }
      }
    }
  }

  // 确保分类与常量列表匹配
  String _getCorrectedCategory(String category) {
    // 如果分类在常量列表中，直接返回
    if (ItemCategories.categories.contains(category)) {
      return category;
    }

    // 类似的分类名称修正
    Map<String, String> categoryMapping = {
      '电子设备': 'electronics',
      '电子产品': 'electronics',
      'Electronic': 'electronics',
      '书本': 'books',
      '书籍': 'books',
      '服装': 'clothing',
      '衣物': 'clothing',
      'Clothing': 'clothing',
      'Books': 'books',
      'Others': 'others',
      '其它': 'others',
      '家居用品': 'household',
      '厨房用品': 'kitchen',
      '运动用品': 'sports',
      '玩具': 'toys',
      '办公用品': 'office',
    };

    // 如果有映射，返回映射的值
    if (categoryMapping.containsKey(category)) {
      return categoryMapping[category]!;
    }

    // 默认返回第一个分类
    return ItemCategories.categories.first;
  }
}
