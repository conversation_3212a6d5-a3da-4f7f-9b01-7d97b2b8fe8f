import 'package:flutter/material.dart';
import 'dart:async';

import 'package:intl/date_symbol_data_local.dart';
import 'package:calculate_cost_flutter/l10n/app_localizations.dart';

import '../services/api_auth_service.dart';
import '../services/purchase_service.dart';

// 是否使用API服务
const bool useApi = false;

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacity;
  late Animation<double> _scale;
  String _initializationStatus = '';

  @override
  void initState() {
    super.initState();

    // 创建动画控制器
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // 透明度动画
    _opacity = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.65, curve: Curves.easeInOut),
      ),
    );

    // 缩放动画
    _scale = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.65, curve: Curves.easeInOut),
      ),
    );

    // 启动动画
    _controller.forward();

    // 设置初始状态并开始异步初始化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final localizations = AppLocalizations.of(context);
      setState(
        () =>
            _initializationStatus =
                localizations?.splashInitializing ?? 'Initializing...',
      );
      _initializeApp();
    });
  }

  Future<void> _initializeApp() async {
    final localizations = AppLocalizations.of(context);

    try {
      // 显示简洁友好的加载状态
      setState(
        () => _initializationStatus = localizations?.splashLoading ?? '正在启动...',
      );

      // 静默初始化所有服务，不显示技术细节给用户
      await initializeDateFormatting('zh_CN', null);
      await initializeDateFormatting('en_US', null);

      final authService = ApiAuthService();
      await authService.initialize();

      final purchaseService = PurchaseService();
      await purchaseService.initialize();

      // 确保启动页面至少显示2秒，给用户足够的视觉体验
      await Future.delayed(const Duration(milliseconds: 2000));

      setState(
        () =>
            _initializationStatus =
                localizations?.splashWelcome ?? '欢迎使用 CostTrack',
      );

      // 等待一小段时间显示完成状态
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    } catch (e) {
      final errorMessage =
          localizations?.splashInitializationFailed(e.toString()) ??
          'Initialization failed: $e';
      setState(() => _initializationStatus = errorMessage);
      // 即使失败也要导航，避免用户卡在启动页
      await Future.delayed(const Duration(seconds: 2));
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.primary,
      body: Center(
        child: AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return Opacity(
              opacity: _opacity.value,
              child: Transform.scale(
                scale: _scale.value,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 使用应用图标图像
                    Image.asset(
                      'assets/icons/app_icon.png',
                      width: 100,
                      height: 100,
                    ),
                    const SizedBox(height: 20),
                    // 应用名称
                    const Text(
                      'CostTrack',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    // 应用描述或标语
                    Text(
                      localizations?.appSlogan ?? '智能追踪您的物品成本',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 40),
                    // 简洁的加载指示器和状态
                    Column(
                      children: [
                        const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),
                        Text(
                          _initializationStatus,
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.8),
                            fontSize: 16,
                            fontWeight: FontWeight.w300,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
