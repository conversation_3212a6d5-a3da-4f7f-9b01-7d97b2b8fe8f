import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:developer' as developer;

import '../services/api_auth_service.dart';
import '../services/base_auth_service.dart';
import '../services/payment_service.dart';
import '../services/purchase_service.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:calculate_cost_flutter/l10n/app_localizations.dart';

class MembershipScreen extends StatefulWidget {
  const MembershipScreen({super.key});

  @override
  _MembershipScreenState createState() => _MembershipScreenState();
}

class _MembershipScreenState extends State<MembershipScreen> {
  String? _errorMessage;
  final bool _isLoading = false;
  bool _isGooglePaySupported = false;

  bool _isRestoring = false;

  // 为每个会员选项添加单独的加载状态
  final Map<String, bool> _loadingStates = {
    'Lifetime': false,
    'Annual': false,
    'Monthly': false,
  };

  // 会员选项
  final Map<String, Map<String, dynamic>> _membershipOptions = {
    'Lifetime': {
      'price': '\$9.99',
      'description':
          'One-time payment for lifetime access - unlock all premium features permanently, the most cost-effective for long-term use',
      'icon': Icons.diamond,
      'iconColor': Colors.amber.shade700,
      'discount': 'bestValue',
      'isOneTime': true,
    },
    'Annual': {
      'price': '\$3.99',
      'description':
          'Unlock all premium features and enjoy a worry-free experience for the whole year',
      'icon': Icons.workspace_premium,
      'iconColor': Colors.green.shade600,
      'discount': 'save50',
      'isOneTime': false,
      'duration': const Duration(days: 365),
    },
    'Monthly': {
      'price': '\$0.99',
      'description':
          'Start or cancel anytime, easily experience all premium features',
      'icon': Icons.verified,
      'iconColor': Colors.blue.shade500,
      'discount': '',
      'isOneTime': false,
      'duration': const Duration(days: 30),
    },
  };

  // 简化日志方法，只记录到开发者日志，不再显示在UI上
  void _log(String message, {String? error}) {
    developer.log(message, name: 'MembershipScreen', error: error);
  }

  @override
  void initState() {
    super.initState();
    _log('MembershipScreen初始化');

    _checkGooglePaySupport();

    // 加载产品信息
    _loadProducts();

    // 确保所有加载状态初始化为false
    for (var key in _loadingStates.keys) {
      _loadingStates[key] = false;
    }
  }

  @override
  void dispose() {
    // 清理资源
    super.dispose();
  }

  // 检查Google Pay支持
  Future<void> _checkGooglePaySupport() async {
    _log('开始检查Google Pay支持状态');
    try {
      final paymentService = PaymentService();
      final isSupported = await paymentService.isGooglePaySupported();

      if (mounted) {
        setState(() {
          _isGooglePaySupported = isSupported;
        });
      }

      _log('Google Pay支持状态检查完成: $_isGooglePaySupported');
    } catch (e) {
      _log('检查Google Pay支持出错: $e', error: e.toString());
    }
  }

  // 加载产品信息
  Future<void> _loadProducts() async {
    _log('开始加载应用内购买产品信息');
    try {
      final paymentService = PaymentService();
      await paymentService.purchaseService.loadProducts();
      setState(() {
        // 更新界面
      });
      _log('产品信息加载完成，找到${paymentService.purchaseService.products.length}个产品');

      // 打印产品信息
      for (final product in paymentService.purchaseService.products) {
        _log('产品: ${product.id}, 标题: ${product.title}, 价格: ${product.price}');
      }
    } catch (e) {
      _log('加载产品信息出错: $e', error: e.toString());
    }
  }

  // 处理支付
  Future<void> _handlePayment(
    String planName,
    String price,
    bool isOneTime,
    Duration? duration,
  ) async {
    _log(
      '用户点击购买: 计划=$planName, 价格=$price, 一次性=$isOneTime, 时长=${duration?.inDays ?? "永久"}天',
    );
    try {
      setState(() {
        _loadingStates[planName] = true;
        _errorMessage = null;
      });
      _log('更新界面状态: 加载中');

      // 根据计划名称获取实际产品ID
      String productId;
      if (planName.contains('Monthly')) {
        productId = PurchaseService.monthlySubscriptionId;
      } else if (planName.contains('Annual')) {
        productId = PurchaseService.annualSubscriptionId;
      } else if (planName.contains('Lifetime')) {
        productId = PurchaseService.lifetimeMembershipId;
      } else {
        productId = planName; // 兜底使用计划名称
      }

      // 获取产品价格
      double amount = 0.99; // 默认金额

      // 获取设备默认货币
      String currency = 'CNY'; // 默认使用人民币

      // 从商店获取实际价格信息
      final paymentService = PaymentService();
      final product = paymentService.purchaseService.getProduct(productId);

      if (product != null) {
        // 提取价格字符串中的数字部分，移除货币符号
        final priceString = product.price.replaceAll(RegExp(r'[^\d.]'), '');
        amount = double.tryParse(priceString) ?? amount;
        _log('从商店获取价格: $productId -> $amount');

        // 尝试从商店价格中获取货币信息
        final currencyRegex = RegExp(r'^[^\d]+');
        final currencyMatch = currencyRegex.firstMatch(product.price);
        if (currencyMatch != null) {
          // 取出货币符号对应的货币代码（简单处理，实际应用可能需要更复杂的映射）
          final symbol = currencyMatch.group(0)?.trim() ?? '';
          if (symbol == '¥') {
            currency = 'CNY';
          } else if (symbol == '\$') {
            currency = 'USD';
          } else if (symbol == '€') {
            currency = 'EUR';
          }
          _log('检测到商店货币: $symbol -> $currency');
        }
      } else {
        // 如果没有找到产品，使用UI上的价格
        final priceString = price.replaceAll(RegExp(r'[^\d.]'), '');
        amount = double.tryParse(priceString) ?? amount;
        _log('使用UI价格: $price -> $amount');
      }

      // 保存会员选项信息，供购买完成后处理
      final membershipOptions = {'isOneTime': isOneTime, 'duration': duration};

      // 将会员选项传递给购买服务，以便购买验证后进行处理
      paymentService.purchaseService.setMembershipOptions(membershipOptions);

      // 调用支付服务
      _log('准备调用支付服务，使用产品ID：$productId, 金额: $amount, 货币: $currency');
      final paymentResult = await paymentService.processPayment(
        productId: productId,
        amount: amount,
        method: PaymentMethod.googlePay, // 默认使用Google Pay
        currency: currency,
      );
      _log('支付结果: $paymentResult');

      if (!paymentResult) {
        _log('支付失败或取消，更新错误状态');
        setState(() {
          _errorMessage = paymentService.lastErrorMessage ?? '支付未成功，请稍后再试';
          _loadingStates[planName] = false;
        });
      } else {
        _log('支付已启动，等待支付结果验证');
        // 支付结果会通过购买服务的回调处理，这里只需要重置状态
        setState(() {
          _loadingStates[planName] = false;
        });
      }
    } catch (e) {
      _log('支付过程异常: ${e.toString()}', error: e.toString());
      setState(() {
        _errorMessage = '支付过程中出错: ${e.toString()}';
        _loadingStates[planName] = false;
      });
    }
  }

  // 恢复购买
  Future<void> _restorePurchases() async {
    _log('开始恢复购买流程');
    try {
      setState(() {
        _isRestoring = true;
        _errorMessage = null;
      });

      final localizations = AppLocalizations.of(context);

      // 显示正在恢复的提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(localizations?.restoringPurchases ?? '正在恢复购买...'),
          duration: const Duration(seconds: 2),
        ),
      );

      // 获取购买服务
      final purchaseService = Provider.of<PurchaseService>(
        context,
        listen: false,
      );

      // 调用恢复购买方法
      await purchaseService.restorePurchases();

      // 购买结果处理会由PurchaseService的回调处理

      // 检查是否已经恢复了会员身份
      if (mounted) {
        final authService = Provider.of<BaseAuthService>(
          context,
          listen: false,
        );
        await authService.refreshUserProfile();
      }

      // 显示恢复结果
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              purchaseService.purchaseError
                  ? (localizations?.purchaseRestoreFailed ?? '恢复购买失败')
                  : (localizations?.purchaseRestoreSuccess ?? '购买恢复成功'),
            ),
            backgroundColor:
                purchaseService.purchaseError ? Colors.red : Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      _log('恢复购买过程出错: ${e.toString()}', error: e.toString());
      if (mounted) {
        setState(() {
          _errorMessage = '恢复购买过程中出错: ${e.toString()}';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRestoring = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<BaseAuthService>(context);

    // 尝试刷新用户信息
    if (!_isLoading) {
      // 使用Future.microtask避免在build过程中调用setState
      Future.microtask(() async {
        await authService.refreshUserProfile();
      });
    }

    final isPremium = authService.isPremium;
    final isExpired = authService.isMembershipExpired;
    // 获取本地化字符串
    final localizations = AppLocalizations.of(context);

    _log('构建会员页面: 是否会员=$isPremium, 是否过期=$isExpired');

    return Scaffold(
      appBar: AppBar(title: Text(localizations?.membership ?? '会员服务')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 错误消息
            if (_errorMessage != null)
              Container(
                padding: const EdgeInsets.all(8),
                margin: const EdgeInsets.only(bottom: 16),
                color: Colors.red.shade100,
                child: Text(
                  _errorMessage!,
                  style: TextStyle(color: Colors.red.shade900),
                ),
              ),

            // 添加恢复购买按钮
            if (!isPremium || isExpired)
              Container(
                width: double.infinity,
                margin: const EdgeInsets.only(bottom: 16),
                child: OutlinedButton.icon(
                  icon: const Icon(Icons.restore),
                  label: Text(localizations?.restorePurchases ?? '恢复购买'),
                  onPressed: _isRestoring ? null : _restorePurchases,
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),

            // 会员权益说明
            Text(
              localizations?.membershipBenefits ?? 'Membership Benefits',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            _buildFeatureItem(
              icon: Icons.analytics_outlined,
              iconColor: Colors.deepPurple,
              title:
                  localizations?.advancedAnalytics ?? 'Advanced Data Analytics',
              description:
                  localizations?.advancedAnalyticsDesc ??
                  'Unlock detailed spending trend charts and smart consumption recommendations',
            ),
            _buildFeatureItem(
              icon: Icons.cloud_sync,
              iconColor: Colors.indigo,
              title: localizations?.cloudBackup ?? 'Cloud Backup',
              description:
                  localizations?.cloudBackupDesc ??
                  'Automatically protect your important data, never lose it',
            ),
            _buildFeatureItem(
              icon: Icons.devices_other,
              iconColor: Colors.teal,
              title: localizations?.multiDeviceSync ?? 'Multi-device Sync',
              description:
                  localizations?.multiDeviceSyncDesc ??
                  'Seamlessly switch between phone, tablet, and computer',
            ),
            _buildFeatureItem(
              icon: Icons.add_chart,
              iconColor: Colors.amber.shade800,
              title: localizations?.unlimitedItems ?? 'Unlimited Items',
              description:
                  localizations?.unlimitedItemsDesc ??
                  'Break through the 30-item limit, add all your valuable items',
            ),
            _buildFeatureItem(
              icon: Icons.color_lens,
              iconColor: Colors.pink,
              title: localizations?.customThemes ?? 'Custom Themes',
              description:
                  localizations?.customThemesDesc ??
                  'Personalized interface and more visual options',
            ),
            _buildFeatureItem(
              icon: Icons.block,
              iconColor: Colors.red.shade600,
              title: localizations?.adFreeExperience ?? 'Ad-Free Experience',
              description:
                  localizations?.adFreeExperienceDesc ??
                  'Focus on using the app without any distractions',
            ),
            _buildFeatureItem(
              icon: Icons.bolt,
              iconColor: Colors.orange,
              title: 'Fast Performance',
              description: 'Enhanced speed and performance for all operations',
            ),
            _buildFeatureItem(
              icon: Icons.support_agent,
              iconColor: Colors.green,
              title: 'Priority Support',
              description: 'Get faster responses from our support team',
            ),
            const SizedBox(height: 24),

            // 会员选项
            if (!isPremium || isExpired) ...[
              Text(
                localizations?.chooseMembershipPlan ??
                    'Choose Your Membership Plan',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // 会员选项卡片
              ..._membershipOptions.entries.map((entry) {
                final String planName = entry.key;
                final Map<String, dynamic> details = entry.value;
                return _buildMembershipCard(title: planName, details: details);
              }),
            ],
          ],
        ),
      ),
    );
  }

  // 创建会员权益项
  Widget _buildFeatureItem({
    required IconData icon,
    Color iconColor = Colors.blue,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: iconColor),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建会员卡片，添加实际产品价格信息
  Widget _buildMembershipCard({
    required String title,
    required Map<String, dynamic> details,
  }) {
    // 获取产品信息
    String priceText = details['price'];
    String description = details['description'];
    final localizations = AppLocalizations.of(context);

    // 直接使用已修改过的描述文本
    String descriptionText = description;

    // 获取本地化的标题
    String localizedTitle;
    switch (title) {
      case 'Lifetime':
        localizedTitle = localizations?.lifetimeMembership ?? 'Lifetime';
        break;
      case 'Annual':
        localizedTitle = localizations?.annualMembership ?? 'Annual';
        break;
      case 'Monthly':
        localizedTitle = localizations?.monthlyMembership ?? 'Monthly';
        break;
      default:
        localizedTitle = title;
    }

    // 获取本地化的折扣文本
    String discountText = details['discount'];
    if (discountText == 'bestValue') {
      discountText = localizations?.bestValue ?? 'Best Value';
    } else if (discountText == 'save50') {
      discountText = localizations?.save50 ?? 'Save 50%';
    }

    // 根据标题确定产品ID
    String productId;
    if (title.contains('Monthly')) {
      productId = PurchaseService.monthlySubscriptionId;
    } else if (title.contains('Annual')) {
      productId = PurchaseService.annualSubscriptionId;
    } else if (title.contains('Lifetime')) {
      productId = PurchaseService.lifetimeMembershipId;
    } else {
      productId = title; // 兜底使用标题
    }

    // 尝试获取实际产品价格
    final paymentService = PaymentService();
    ProductDetails? product = paymentService.purchaseService.getProduct(
      productId,
    );

    // 如果找到实际产品，使用实际价格
    if (product != null) {
      priceText = product.price;
      _log('使用商店价格: $title => ${product.price}');
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      details['icon'],
                      color:
                          details['iconColor'] ??
                          Theme.of(context).primaryColor,
                      size: 28,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      localizedTitle,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                if (discountText.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      discountText,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Text(descriptionText, style: TextStyle(color: Colors.grey[600])),
            const SizedBox(height: 12),
            Center(
              child: Text(
                priceText,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton.icon(
              onPressed:
                  _loadingStates[title] == true
                      ? null
                      : () => _handlePayment(
                        title,
                        priceText,
                        details['isOneTime'],
                        details['duration'],
                      ),
              icon:
                  _loadingStates[title] == true
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Icon(Icons.shopping_cart),
              label: Text(
                _loadingStates[title] == true
                    ? (localizations?.processing ?? 'Processing...')
                    : (localizations?.buyNow ?? 'Buy Now'),
              ),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 45),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
