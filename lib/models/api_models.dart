import 'user.dart';
import 'item.dart';
import 'feedback.dart';

// API 响应基类
class ApiResponse<T> {
  final T? data;
  final String? error;
  final String? code;

  ApiResponse({this.data, this.error, this.code});

  bool get isSuccess => error == null && data != null;

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    if (json.containsKey('error')) {
      return ApiResponse<T>(
        error: json['error'] as String,
        code: json['code'] as String?,
      );
    }
    return ApiResponse<T>(data: fromJsonT(json));
  }
}

// 用户登录响应
class LoginResponse {
  final User user;
  final String token;

  LoginResponse({required this.user, required this.token});

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      user: User.fromApi<PERSON><PERSON>(json['user']),
      token: json['token'] as String,
    );
  }
}

// 用户登录请求
class LoginRequest {
  final String email;
  final String password;

  LoginRequest({required this.email, required this.password});

  Map<String, dynamic> toJson() {
    return {'email': email, 'password': password};
  }
}

// 发送验证码请求
class VerificationRequest {
  final String email;
  final String purpose;

  VerificationRequest({required this.email, required this.purpose});

  Map<String, dynamic> toJson() {
    return {'email': email, 'purpose': purpose};
  }
}

// 用户注册请求
class RegisterRequest {
  final String email;
  final String password;
  final String displayName;
  final String verifyCode;

  RegisterRequest({
    required this.email,
    required this.password,
    required this.displayName,
    required this.verifyCode,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
      'displayName': displayName,
      'verifyCode': verifyCode,
    };
  }
}

// 物品响应包装类
class ItemResponse {
  final Item item;

  ItemResponse({required this.item});

  factory ItemResponse.fromJson(Map<String, dynamic> json) {
    return ItemResponse(item: Item.fromApiJson(json['item']));
  }
}

// 物品列表响应包装类
class ItemsResponse {
  final List<Item> items;

  ItemsResponse({required this.items});

  factory ItemsResponse.fromJson(Map<String, dynamic> json) {
    return ItemsResponse(
      items:
          (json['items'] as List)
              .map((item) => Item.fromApiJson(item))
              .toList(),
    );
  }
}

// 反馈响应包装类
class FeedbackResponse {
  final UserFeedback feedback;

  FeedbackResponse({required this.feedback});

  factory FeedbackResponse.fromJson(Map<String, dynamic> json) {
    return FeedbackResponse(feedback: UserFeedback.fromJson(json));
  }
}

// 通用消息响应
class MessageResponse {
  final String message;

  MessageResponse({required this.message});

  factory MessageResponse.fromJson(Map<String, dynamic> json) {
    return MessageResponse(message: json['message'] as String);
  }
}

// 重置密码请求
class ResetPasswordRequest {
  final String email;
  final String verificationCode;
  final String newPassword;

  ResetPasswordRequest({
    required this.email,
    required this.verificationCode,
    required this.newPassword,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'verification_code': verificationCode,
      'new_password': newPassword,
    };
  }
}

/// 创建订单请求模型
class CreateOrderRequest {
  final String productId;
  final double amount;
  final String? currency;
  final String? description;

  CreateOrderRequest({
    required this.productId,
    required this.amount,
    this.currency,
    this.description,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'productId': productId,
      'amount': amount,
    };
    if (currency != null) data['currency'] = currency;
    if (description != null) data['description'] = description;
    return data;
  }
}

/// 订单响应模型
class OrderResponse {
  final String orderId;
  final String productId;
  final double amount;
  final String currency;
  final String createdAt;
  final String status;
  final String? description;

  OrderResponse({
    required this.orderId,
    required this.productId,
    required this.amount,
    required this.currency,
    required this.createdAt,
    required this.status,
    this.description,
  });

  factory OrderResponse.fromJson(Map<String, dynamic> json) {
    return OrderResponse(
      orderId: json['orderId'],
      productId: json['productId'],
      amount: json['amount'].toDouble(),
      currency: json['currency'],
      createdAt: json['createdAt'],
      status: json['status'],
      description: json['description'],
    );
  }
}

/// 验证购买请求模型
class PurchaseValidationRequest {
  final String productId;
  final String purchaseToken;
  final String orderId;
  final String? packageName;

  PurchaseValidationRequest({
    required this.productId,
    required this.purchaseToken,
    required this.orderId,
    this.packageName,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'productId': productId,
      'purchaseToken': purchaseToken,
      'orderId': orderId,
    };
    if (packageName != null) data['packageName'] = packageName;
    return data;
  }
}

/// 验证购买响应模型
class PurchaseValidationResponse {
  final bool isActive;
  final String type;
  final String expiryDate;
  final bool autoRenewing;
  final bool isOneTime;

  PurchaseValidationResponse({
    required this.isActive,
    required this.type,
    required this.expiryDate,
    required this.autoRenewing,
    required this.isOneTime,
  });

  factory PurchaseValidationResponse.fromJson(Map<String, dynamic> json) {
    return PurchaseValidationResponse(
      isActive: json['isActive'] ?? false,
      type: json['type'] ?? 'unknown',
      expiryDate: json['expiryDate'] ?? '',
      autoRenewing: json['autoRenewing'] ?? false,
      isOneTime: json['isOneTime'] ?? false,
    );
  }
}
