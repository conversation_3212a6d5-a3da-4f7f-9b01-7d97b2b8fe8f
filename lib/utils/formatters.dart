import 'package:intl/intl.dart';
import 'package:calculate_cost_flutter/l10n/app_localizations.dart';
import 'package:flutter/material.dart';

class Formatters {
  // 金额格式化
  static String formatCurrency(double amount) {
    final formatter = NumberFormat.currency(symbol: '¥', decimalDigits: 2);
    return formatter.format(amount);
  }

  // 数字格式化（添加千位分隔符）
  static String formatNumber(double number) {
    final formatter = NumberFormat('#,##0.00');
    return formatter.format(number);
  }

  // 日期格式化 (年-月-日)
  static String formatDate(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }

  // 长日期格式化 (年月日 星期几)
  static String formatLongDate(DateTime date) {
    return DateFormat('yyyy年MM月dd日 EEEE', 'zh_CN').format(date);
  }

  // 短日期格式化 (年-月-日)
  static String formatShortDate(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }

  // 计算两个日期之间的时间差，返回"X天前"格式
  static String timeAgo(DateTime date, [BuildContext? context]) {
    final now = DateTime.now();
    final difference = now.difference(date);
    final localizations = context != null ? AppLocalizations.of(context) : null;

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return localizations != null
          ? '$years${localizations.yearsAgo}'
          : '$years年前';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return localizations != null
          ? '$months${localizations.monthsAgo}'
          : '$months个月前';
    } else if (difference.inDays > 0) {
      return localizations != null
          ? '${difference.inDays}${localizations.daysAgo}'
          : '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return localizations != null
          ? '${difference.inHours}${localizations.hoursAgo}'
          : '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return localizations != null
          ? '${difference.inMinutes}${localizations.minutesAgo}'
          : '${difference.inMinutes}分钟前';
    } else {
      return localizations?.justNow ?? '刚刚';
    }
  }

  // 格式化每日成本
  static String formatDailyCost(double? cost, [BuildContext? context]) {
    if (cost == null) {
      if (context != null && AppLocalizations.of(context) != null) {
        final days = AppLocalizations.of(context)?.days ?? '天';
        return '¥0.00/$days';
      }
      return '¥0.00/天';
    }

    if (context != null && AppLocalizations.of(context) != null) {
      final days = AppLocalizations.of(context)?.days ?? '天';
      return '${formatCurrency(cost)}/$days';
    }

    return '${formatCurrency(cost)}/天';
  }

  // 获取分类的本地化名称
  static String getCategoryName(String category, BuildContext context) {
    final localizations = AppLocalizations.of(context);
    if (localizations == null) return category;

    switch (category) {
      case 'electronics':
        return localizations.categoryElectronics;
      case 'household':
        return localizations.categoryHousehold;
      case 'kitchen':
        return localizations.categoryKitchen;
      case 'books':
        return localizations.categoryBooks;
      case 'clothing':
        return localizations.categoryClothing;
      case 'sports':
        return localizations.categorySports;
      case 'toys':
        return localizations.categoryToys;
      case 'office':
        return localizations.categoryOffice;
      case 'others':
        return localizations.categoryOthers;
      default:
        return category;
    }
  }
}
