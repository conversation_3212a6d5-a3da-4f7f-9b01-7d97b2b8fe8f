import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:math';
import '../utils/constants.dart';

class ImageHelper {
  // DNS缓存，用于存储域名到IP的映射
  static final Map<String, String> _dnsCache = {};

  // 获取相对路径（从完整路径中)
  static String getRelativePath(String? fullPath) {
    if (fullPath == null || fullPath.isEmpty) {
      return '';
    }

    // 如果是网络路径，直接返回
    if (isNetworkImagePath(fullPath)) {
      return fullPath;
    }

    // 提取相对路径 (images/item_xxx.jpg)
    try {
      final segments = fullPath.split('/');
      final imagesIndex = segments.indexOf('images');
      if (imagesIndex >= 0 && imagesIndex < segments.length - 1) {
        return 'images/${segments[imagesIndex + 1]}';
      }
    } catch (e) {
      debugPrint('提取相对路径出错: $e');
    }

    return fullPath; // 如果无法提取，返回原路径
  }

  // 根据相对路径获取实际文件路径
  static Future<String> getActualImagePath(String? relativePath) async {
    if (relativePath == null || relativePath.isEmpty) {
      return '';
    }

    // 如果是网络路径，直接返回
    if (isNetworkImagePath(relativePath)) {
      return relativePath;
    }

    // 如果已经是绝对路径，直接返回
    if (relativePath.startsWith('/')) {
      return relativePath;
    }

    try {
      final appDocDir = await getApplicationDocumentsDirectory();
      return '${appDocDir.path}/$relativePath';
    } catch (e) {
      debugPrint('获取实际图片路径出错: $e');
      return relativePath;
    }
  }

  // 将图片从临时目录复制到应用的永久存储目录
  static Future<String> saveImageToAppDirectory(String sourcePath) async {
    try {
      // 首先检查文件是否存在
      final File sourceFile = File(sourcePath);
      if (!await sourceFile.exists()) {
        throw Exception('源文件不存在: $sourcePath');
      }

      // 简单检查文件大小，过小的文件可能不是有效图片
      final int fileSize = await sourceFile.length();
      if (fileSize < 100) {
        // 简单检查，文件过小可能不是图片
        throw Exception('文件过小，可能不是有效的图片');
      }

      // 获取应用文档目录
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      final String appDocPath = appDocDir.path;

      // 创建图片存储子目录
      final Directory imageDir = Directory('$appDocPath/images');
      if (!await imageDir.exists()) {
        await imageDir.create(recursive: true);
      }

      // 生成唯一文件名
      final String fileName =
          'item_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final String destinationPath = '${imageDir.path}/$fileName';

      // 复制图片文件到应用文档目录
      await sourceFile.copy(destinationPath);

      // 返回相对路径而不是完整路径
      return 'images/$fileName';
    } catch (e) {
      debugPrint('保存图片时出错: $e');
      rethrow;
    }
  }

  // 检查图片文件是否存在
  static Future<bool> imageFileExists(String? path) async {
    if (path == null || path.isEmpty) {
      return false;
    }

    // 检查是否为URL格式
    if (path.startsWith('http://') || path.startsWith('https://')) {
      debugPrint('路径是URL格式，不检查本地文件: $path');
      return true; // 假设URL是有效的
    }

    try {
      // 如果是相对路径，获取完整路径
      String fullPath = path;
      if (!path.startsWith('/')) {
        final appDocDir = await getApplicationDocumentsDirectory();
        fullPath = '${appDocDir.path}/$path';
      }

      final File file = File(fullPath);
      final bool exists = await file.exists();

      // 记录状态以便调试
      debugPrint('检查图片文件 [${exists ? "存在" : "不存在"}]: $fullPath');

      // 简单检查文件大小
      if (exists) {
        final int fileSize = await file.length();
        return fileSize > 100; // 文件必须大于100字节才算有效
      }

      return false;
    } catch (e) {
      debugPrint('检查图片文件是否存在时出错: $e');
      return false;
    }
  }

  // 删除图片文件
  static Future<bool> deleteImageFile(String? path) async {
    if (path == null || path.isEmpty) {
      return false;
    }

    // 如果是URL格式，不需要删除本地文件
    if (path.startsWith('http://') || path.startsWith('https://')) {
      return true;
    }

    try {
      // 如果是相对路径，获取完整路径
      String fullPath = path;
      if (!path.startsWith('/')) {
        final appDocDir = await getApplicationDocumentsDirectory();
        fullPath = '${appDocDir.path}/$path';
      }

      final File file = File(fullPath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('删除图片文件时出错: $e');
      return false;
    }
  }

  // 构建图片错误显示小部件
  static Widget buildImageErrorWidget({String? message}) {
    return Container(
      color: Colors.grey[200],
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.broken_image, size: 24, color: Colors.grey),
            if (message != null)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  message,
                  style: TextStyle(color: Colors.grey[600], fontSize: 10),
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 判断路径是否为URL
  static bool isNetworkImagePath(String? path) {
    if (path == null || path.isEmpty) {
      return false;
    }
    return path.startsWith('http://') || path.startsWith('https://');
  }

  // 获取适合Widget显示的图片
  static Future<ImageProvider> getImageProviderSafe(String? path) async {
    if (path == null || path.isEmpty) {
      // 使用内存中的透明占位图，而不是尝试加载资源文件
      return MemoryImage(kTransparentImage);
    }

    if (isNetworkImagePath(path)) {
      return NetworkImage(path);
    } else {
      // 如果是相对路径，获取完整路径
      String fullPath = path;
      if (!path.startsWith('/')) {
        final appDocDir = await getApplicationDocumentsDirectory();
        fullPath = '${appDocDir.path}/$path';
      }

      // 检查文件是否存在
      final file = File(fullPath);
      if (await file.exists()) {
        return FileImage(file);
      } else {
        debugPrint('图片文件不存在，使用占位图: $fullPath');
        // 使用内存中的透明占位图，而不是尝试加载资源文件
        return MemoryImage(kTransparentImage);
      }
    }
  }

  // 旧方法保留兼容
  static ImageProvider getImageProvider(String? path) {
    if (path == null || path.isEmpty) {
      // 使用内存中的透明占位图，而不是尝试加载资源文件
      return MemoryImage(kTransparentImage);
    }

    if (isNetworkImagePath(path)) {
      return NetworkImage(path);
    } else {
      return FileImage(File(path));
    }
  }

  // 1x1像素的透明图片
  static final Uint8List kTransparentImage = Uint8List.fromList([
    0x89,
    0x50,
    0x4E,
    0x47,
    0x0D,
    0x0A,
    0x1A,
    0x0A,
    0x00,
    0x00,
    0x00,
    0x0D,
    0x49,
    0x48,
    0x44,
    0x52,
    0x00,
    0x00,
    0x00,
    0x01,
    0x00,
    0x00,
    0x00,
    0x01,
    0x08,
    0x06,
    0x00,
    0x00,
    0x00,
    0x1F,
    0x15,
    0xC4,
    0x89,
    0x00,
    0x00,
    0x00,
    0x0A,
    0x49,
    0x44,
    0x41,
    0x54,
    0x78,
    0x9C,
    0x63,
    0x00,
    0x01,
    0x00,
    0x00,
    0x05,
    0x00,
    0x01,
    0x0D,
    0x0A,
    0x2D,
    0xB4,
    0x00,
    0x00,
    0x00,
    0x00,
    0x49,
    0x45,
    0x4E,
    0x44,
    0xAE,
    0x42,
    0x60,
    0x82,
  ]);

  // 从网络URL下载图片并保存到本地
  static Future<String?> downloadAndSaveImage(String imageUrl) async {
    int retryCount = 0;
    const int maxRetries = 3;
    const Duration retryDelay = Duration(seconds: 2);

    while (retryCount < maxRetries) {
      try {
        debugPrint('开始下载图片: $imageUrl (第${retryCount + 1}次尝试)');

        // 创建一个有超时设置的客户端
        final client = http.Client();
        final request = http.Request('GET', Uri.parse(imageUrl));
        request.followRedirects = true;

        try {
          final streamedResponse = await client
              .send(request)
              .timeout(const Duration(seconds: 15));

          final response = await http.Response.fromStream(streamedResponse);

          if (response.statusCode != 200) {
            debugPrint('下载图片失败，状态码: ${response.statusCode}');
            retryCount++;
            if (retryCount < maxRetries) {
              debugPrint('等待${retryDelay.inSeconds}秒后重试...');
              await Future.delayed(retryDelay);
              continue;
            }
            return null;
          }

          // 获取应用文档目录
          final Directory appDocDir = await getApplicationDocumentsDirectory();
          final String appDocPath = appDocDir.path;

          // 创建图片存储子目录
          final Directory imageDir = Directory('$appDocPath/images');
          if (!await imageDir.exists()) {
            await imageDir.create(recursive: true);
          }

          // 从URL提取文件名或生成唯一文件名
          String fileName = Uri.parse(imageUrl).pathSegments.last;
          // 加入随机后缀，确保唯一性
          final String randomSuffix = '-${_getRandomString(8)}';

          if (fileName.isEmpty ||
              !fileName.contains('.') ||
              fileName.length > 100) {
            fileName =
                'download_${DateTime.now().millisecondsSinceEpoch}$randomSuffix.jpg';
          } else {
            // 在文件扩展名前添加随机后缀
            final parts = fileName.split('.');
            final extension = parts.removeLast();
            fileName = '${parts.join('.')}$randomSuffix.$extension';
          }

          final String destinationPath = '${imageDir.path}/$fileName';

          // 保存文件
          final File file = File(destinationPath);
          await file.writeAsBytes(response.bodyBytes);

          debugPrint('图片已下载并保存至: $destinationPath');

          // 关闭HTTP客户端
          client.close();

          // 将成功解析的域名添加到缓存
          try {
            final uri = Uri.parse(imageUrl);
            if (retryCount > 0) {
              // 如果是重试成功的，并且使用的是IP地址，将原始域名也添加到缓存
              final originalUri = Uri.parse(imageUrl);
              if (originalUri.host.contains('.')) {
                final regexIp = RegExp(r'^(\d{1,3}\.){3}\d{1,3}$');
                if (!regexIp.hasMatch(uri.host)) {
                  addToDnsCache(uri.host, originalUri.host);
                }
              }
            }
          } catch (e) {
            debugPrint('处理域名缓存时出错: $e');
          }

          // 返回相对路径
          return 'images/$fileName';
        } finally {
          client.close();
        }
      } catch (e) {
        debugPrint('下载并保存图片时出错: $e');

        // 判断是否为DNS解析错误
        final errorMsg = e.toString().toLowerCase();
        final isDnsError =
            errorMsg.contains('failed host lookup') ||
            errorMsg.contains('no address associated with hostname') ||
            errorMsg.contains('errno = 7');

        if (isDnsError) {
          debugPrint('发生DNS解析错误，尝试使用备用方法解析...');

          // 尝试提取域名
          try {
            final uri = Uri.parse(imageUrl);
            final host = uri.host;
            debugPrint('提取的域名: $host');

            // 尝试使用另一个备用URL
            final backupUrl = await _getBackupImageUrl(imageUrl);
            if (backupUrl != null && backupUrl != imageUrl) {
              debugPrint('使用备用URL: $backupUrl');
              imageUrl = backupUrl; // 使用备用URL重试
            }
          } catch (uriError) {
            debugPrint('解析URL出错: $uriError');
          }
        }

        retryCount++;
        if (retryCount < maxRetries) {
          debugPrint('等待${retryDelay.inSeconds}秒后重试...');
          await Future.delayed(retryDelay);
          continue;
        }
      }
    }

    debugPrint('在$maxRetries次尝试后下载图片失败');
    return null;
  }

  // 获取随机字符串
  static String _getRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => chars.codeUnitAt(random.nextInt(chars.length)),
      ),
    );
  }

  // 尝试获取备用图片URL
  static Future<String?> _getBackupImageUrl(String originalUrl) async {
    try {
      // 解析原始URL
      final uri = Uri.parse(originalUrl);

      // 检查是否有缓存的IP
      if (_dnsCache.containsKey(uri.host)) {
        final cachedIP = _dnsCache[uri.host]!;
        debugPrint('使用缓存的IP地址: ${uri.host} -> $cachedIP');
        return originalUrl.replaceFirst(uri.host, cachedIP);
      }

      // 如果是Cloudflare R2存储的图片
      if (uri.host.contains('r2.dev')) {
        // 针对 pub-96e594bb6cf14ee7928e565841450dcf.r2.dev 的特殊处理
        if (uri.host == 'pub-96e594bb6cf14ee7928e565841450dcf.r2.dev') {
          // 使用预先获取的IP地址
          final ipAddress = '************'; // 实际ping得到的IP地址

          // 将IP地址保存到缓存
          _dnsCache[uri.host] = ipAddress;

          // 使用IP地址替换域名
          final newUrl = originalUrl.replaceFirst(uri.host, ipAddress);
          debugPrint('已将R2域名替换为IP地址: $newUrl');
          return newUrl;
        }
      }

      // 如果没有备用URL，返回原始URL以便重试原始链接
      return originalUrl;
    } catch (e) {
      debugPrint('生成备用URL时出错: $e');
      return null;
    }
  }

  // 添加一个域名和IP的映射到DNS缓存
  static void addToDnsCache(String domain, String ipAddress) {
    _dnsCache[domain] = ipAddress;
    debugPrint('添加到DNS缓存: $domain -> $ipAddress');
  }

  // 添加一个方法用于获取替代图片的图标
  static Widget buildCategoryIconWidget({
    required BuildContext context,
    required String? category,
    required double size,
    Color? backgroundColor,
    double? containerSize,
    BorderRadius? borderRadius,
  }) {
    // 获取图标
    final IconData categoryIcon =
        ItemCategories.categoryIcons[category] ?? Icons.help_outline;

    // 获取此分类的颜色
    final Color categoryColor =
        ItemCategories.categoryColors[category] ??
        Theme.of(context).colorScheme.primary;

    final bgColor = backgroundColor ?? categoryColor.withOpacity(0.15);

    return Container(
      width: containerSize ?? 60,
      height: containerSize ?? 60,
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Icon(categoryIcon, size: size, color: categoryColor),
    );
  }
}
