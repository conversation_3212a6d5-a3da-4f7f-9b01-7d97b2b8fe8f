import 'package:flutter/material.dart';
import 'package:calculate_cost_flutter/l10n/app_localizations.dart';
import '../models/item.dart';
import '../utils/formatters.dart';
import '../utils/constants.dart';
import '../utils/image_helper.dart';

class ItemCard extends StatelessWidget {
  final Item item;
  final VoidCallback onTap;

  const ItemCard({super.key, required this.item, required this.onTap});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              _buildLeadingIcon(context),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          ItemCategories.categoryIcons[item.category] ??
                              Icons.category,
                          size: 14,
                          color:
                              ItemCategories.categoryColors[item.category] ??
                              Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          Formatters.getCategoryName(item.category, context),
                          style: TextStyle(
                            fontSize: 14,
                            color:
                                ItemCategories.categoryColors[item.category] ??
                                Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${localizations?.purchaseDate ?? '购买日期'}: ${Formatters.formatDate(item.purchaseDate)}',
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    Formatters.formatCurrency(item.price),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(
                        context,
                      ).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      Formatters.formatDailyCost(item.dailyCost, context),
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLeadingIcon(BuildContext context) {
    // 获取此分类的颜色，或使用默认颜色
    final Color categoryColor =
        ItemCategories.categoryColors[item.category] ??
        Theme.of(context).colorScheme.primary;

    // 获取图标
    final IconData categoryIcon =
        ItemCategories.categoryIcons[item.category] ?? Icons.help_outline;

    // 始终使用分类图标而非图片
    return ImageHelper.buildCategoryIconWidget(
      context: context,
      category: item.category,
      size: 30,
    );
  }

  // 构建分类图标容器
  Widget _buildCategoryIconContainer(IconData icon, Color color) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: color.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Icon(icon, size: 30, color: color),
    );
  }
}
