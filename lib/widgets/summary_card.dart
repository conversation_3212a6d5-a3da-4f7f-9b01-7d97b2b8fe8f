import 'package:flutter/material.dart';
import 'package:calculate_cost_flutter/l10n/app_localizations.dart';
import '../utils/formatters.dart';
import 'package:intl/intl.dart';

class SummaryCard extends StatelessWidget {
  final double totalCost;
  final double averageDailyCost;
  final int itemCount;

  const SummaryCard({
    super.key,
    required this.totalCost,
    required this.averageDailyCost,
    required this.itemCount,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final today = DateTime.now();
    final dailyCost = averageDailyCost.toStringAsFixed(2);

    // 获取当前日期字符串
    final dateStr = DateFormat('yyyy/MM/dd').format(today);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF7B66FF), Color(0xFF5E4AEB)],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 头部区域：总资产标题和日期
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                localizations?.totalInvestment ?? 'Total Assets',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  dateStr,
                  style: const TextStyle(fontSize: 14, color: Colors.white),
                ),
              ),
            ],
          ),

          // 我的资产小标题
          const SizedBox(height: 4),
          Row(
            children: [
              Container(
                width: 3,
                height: 16,
                margin: const EdgeInsets.only(right: 8),
                color: Colors.white,
              ),
              Text(
                localizations?.myData ?? 'My Assets',
                style: const TextStyle(fontSize: 14, color: Colors.white70),
              ),
            ],
          ),

          // 总金额大显示
          const SizedBox(height: 16),
          Text(
            '¥${Formatters.formatNumber(totalCost)}',
            style: const TextStyle(
              fontSize: 40,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),

          // 底部三个指标
          const SizedBox(height: 36),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildIndicator(
                context,
                localizations?.dailyCost ?? 'Today\'s Cost',
                '¥$dailyCost',
              ),
              _buildIndicator(
                context,
                localizations?.itemCount ?? 'Asset Count',
                '$itemCount',
              ),
              _buildIndicator(
                context,
                localizations?.averageDailyCost ?? 'Est. Daily Cost',
                '¥$dailyCost',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildIndicator(BuildContext context, String title, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontSize: 12, color: Colors.white70),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ],
    );
  }
}
