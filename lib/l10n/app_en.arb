{"@@locale": "en", "appTitle": "CostTrack", "login": "<PERSON><PERSON>", "register": "Register", "settings": "Settings", "profile": "Profile", "membership": "Membership Service", "premiumUser": "Premium User", "ordinaryUser": "Ordinary User", "expiryDate": "Expires on", "language": "Language", "followSystem": "Follow System", "english": "English", "chinese": "Chinese", "theme": "Theme", "darkMode": "Dark Mode", "lightMode": "Light Mode", "systemMode": "System Default", "accountAndService": "Account & Services", "myData": "My Assets", "backupAndRestore": "Backup & Restore", "premiumExclusive": "Premium Exclusive", "feedback": "<PERSON><PERSON><PERSON>", "aboutUs": "About Us", "loginRequired": "<PERSON><PERSON> Required", "pleaseLoginFirst": "Please login first", "cancel": "Cancel", "ok": "OK", "logout": "Logout", "logoutConfirm": "Are you sure you want to logout?", "avaliableItems": "Available Items: {count}/30", "@avaliableItems": {"placeholders": {"count": {"type": "Object"}}}, "home": "Home", "category": "Category", "statistics": "Statistics", "noItems": "You haven't added any items yet", "addFirstItem": "Add your first item", "recentlyAdded": "Recently Added Items", "highestDailyCost": "Highest Daily Cost Items", "noData": "No data available", "showAllItems": "Show all items", "showActiveItems": "Show active items only", "loadDemoData": "Load demo data", "userFeedback": "User <PERSON>", "feedbackTip": "We value your feedback. Please let us know your thoughts or report any issues:", "feedbackType": "Feedback Type", "feedbackContent": "Feedback Content", "feedbackContentHint": "Please describe your suggestion or issue in detail...", "contactInfo": "Contact Information (Optional)", "contactInfoHint": "You can leave your email or phone number for us to reply", "submitFeedback": "Submit <PERSON>", "feedbackSuccess": "Thank you for your feedback, we will process it as soon as possible!", "anonymousFeedbackTip": "You are providing anonymous feedback. Sign in for a better experience.", "feedbackAndSuggestions": "Feedback and Suggestions", "noStatsData": "No statistics data available", "statisticsInfo": "Statistics Information", "aboutUsTitle": "About Us", "aboutUsDescription": "CostTrack is a tool designed to help you track and calculate the usage cost of your items.", "addItemTitle": "Add Item", "itemName": "Item Name", "itemCategory": "Category", "purchaseDate": "Purchase Date", "purchasePrice": "Purchase Price", "itemNotes": "Notes (Optional)", "saveItem": "Save Item", "itemImage": "Item Image", "chooseImage": "Choose Image", "takePhoto": "Take Photo", "loadDemoDataTitle": "Load Demo Data", "loadDemoDataConfirm": "This will clear existing data and load sample data. Continue?", "demoDataLoadSuccess": "Sample data loaded successfully!", "apiModeError": "Cannot load demo data in API mode!", "featureSuggestion": "Feature Suggestion", "performanceIssue": "Performance Issue", "userExperience": "User Experience", "membershipService": "Membership Service", "otherIssues": "Other Issues", "totalCost": "Total Cost", "dailyAverage": "Daily Average", "itemCount": "<PERSON><PERSON>", "costDistribution": "Cost Distribution by Category", "statusDistribution": "Status Distribution", "dataRefreshed": "Data refreshed", "refreshData": "Refresh data", "itemOverview": "Item Overview", "categoryDistribution": "Category Distribution", "highestDailyCostItems": "Highest Daily Cost Items", "totalItems": "Total Items", "totalInvestment": "Total Investment", "averageDailyCost": "Average Daily Cost", "daysUsed": "Days Used", "days": "days", "day": "day", "editItemTitle": "<PERSON>em", "updateItem": "Update Item", "pleaseEnterItemName": "Please enter item name", "pleaseEnterPrice": "Please enter price", "pleaseEnterValidPrice": "Please enter a valid price", "pleaseSelectCategory": "Please select a category", "pleaseSelectDate": "Please select purchase date", "selectImageSource": "Select Image Source", "backupRestoreTitle": "Data Backup & Restore", "backupRestoreDescription": "You can backup your item data to the cloud, or restore your data from the cloud.", "backupToCloud": "Backup to Cloud", "restoreFromCloud": "Restore from Cloud", "backupCaution": "Backup and restore will overwrite existing data, please be cautious", "close": "Close", "processingImage": "Processing image...", "networkUnavailable": "Network connection unavailable, please check your network settings", "userInfoError": "Unable to get user information, please log in again", "noItemsToBackup": "No items need to be backed up", "gettingCloudData": "Getting cloud data...", "getCloudDataFailed": "Failed to get cloud data: {error}", "@getCloudDataFailed": {"placeholders": {"error": {"type": "String"}}}, "deletingCloudItems": "Deleting unnecessary items from the cloud...", "deleteCloudItemsFailed": "Failed to delete cloud items: {error}", "@deleteCloudItemsFailed": {"placeholders": {"error": {"type": "String"}}}, "uploadingLocalItems": "Uploading local items to the cloud ({current}/{total})...", "@uploadingLocalItems": {"placeholders": {"current": {"type": "int"}, "total": {"type": "int"}}}, "partialUploadFailed": "Some items failed to upload, synchronized {success} items, failed {failed} items, deleted {deleted} items", "@partialUploadFailed": {"placeholders": {"success": {"type": "int"}, "failed": {"type": "int"}, "deleted": {"type": "int"}}}, "backupSuccess": "Data backup succeeded! Synchronized {added} items, deleted {deleted} items", "@backupSuccess": {"placeholders": {"added": {"type": "int"}, "deleted": {"type": "int"}}}, "updatingItems": "Updating items ({current}/{total})...", "@updatingItems": {"placeholders": {"current": {"type": "int"}, "total": {"type": "int"}}}, "backupSuccessWithUpdate": "Data backup succeeded! Synchronized {added} items, deleted {deleted} items, updated {updated} items", "@backupSuccessWithUpdate": {"placeholders": {"added": {"type": "int"}, "deleted": {"type": "int"}, "updated": {"type": "int"}}}, "backupError": "Error during backup: {error}", "@backupError": {"placeholders": {"error": {"type": "String"}}}, "restoreSuccess": "Successfully restored {count} items", "@restoreSuccess": {"placeholders": {"count": {"type": "int"}}}, "restoreFailed": "<PERSON><PERSON> failed: {error}", "@restoreFailed": {"placeholders": {"error": {"type": "String"}}}, "restoreError": "Error during restore: {error}", "@restoreError": {"placeholders": {"error": {"type": "String"}}}, "premiumFeature": "Premium Feature", "upgradeToUsePremium": "Backup and restore features are only available to premium users. Upgrade to premium to use cloud backup and restore, making your data more secure.", "upgradeToMember": "Upgrade Membership", "notNow": "Not Now", "itemLimitTitle": "Item limit reached", "itemLimitDescription": "Free users can add up to 30 items. Please upgrade to premium to unlock unlimited item addition and cloud synchronization services.", "lateTalk": "Maybe Later", "categoryLabel": "Category: {category}", "@categoryLabel": {"placeholders": {"category": {"type": "String"}}}, "basicInfo": "Basic Information", "usageDays": "{days} days", "@usageDays": {"placeholders": {"days": {"type": "int"}}}, "costAnalysis": "Cost Analysis", "netCost": "Net Cost", "dailyCost": "Daily Cost", "weeklyCost": "{cost}/week", "@weeklyCost": {"placeholders": {"cost": {"type": "String"}}}, "monthlyCost": "{cost}/month", "@monthlyCost": {"placeholders": {"cost": {"type": "String"}}}, "yearlyCost": "{cost}/year", "@yearlyCost": {"placeholders": {"cost": {"type": "String"}}}, "costCap": "Note: Long-term cost is capped at the item's total value ({value})", "@costCap": {"placeholders": {"value": {"type": "String"}}}, "netCostCap": "Note: Long-term cost is capped at the item's net cost ({value})", "@netCostCap": {"placeholders": {"value": {"type": "String"}}}, "notes": "Notes", "itemStatus": "Item Status", "changeStatus": "Change Status", "statusActive": "Active", "statusDamaged": "Damaged", "statusInactive": "Inactive", "statusTransfer": "Transferred", "statusDescription": "Status Description:\nActive - Item is normally counted in daily cost\nDamaged - Item is damaged and no longer counted in cost\nInactive - Item is no longer in use and not counted in cost\nTransferred - Item has been transferred to others and not counted in cost", "confirmDelete": "Confirm Delete", "deleteConfirmation": "Are you sure you want to delete \"{name}\"? This action cannot be undone.", "@deleteConfirmation": {"placeholders": {"name": {"type": "String"}}}, "delete": "Delete", "transferItem": "Transfer Item", "transferDescription": "Please enter the amount received when transferring out, this will help calculate the actual cost of the item.", "transferAmount": "Transfer Amount", "transferNote": "After transfer, the item will automatically be set to \"Inactive\" status and no longer counted in daily cost.", "confirmTransfer": "Confirm Transfer", "pleaseEnterTransferAmount": "Please enter transfer amount", "pleaseEnterValidTransferAmount": "Please enter a valid transfer amount", "imageNotFound": "Image Not Found", "cannotLoadImage": "Cannot Load Image", "categoryElectronics": "Electronics", "categoryMobile": "Mobile", "categoryComputer": "Computer", "categoryHousehold": "Household", "categoryKitchen": "Kitchen", "categoryBooks": "Books", "categoryClothing": "Clothing", "categorySports": "Sports", "categoryToys": "Toys", "categoryOffice": "Office", "categoryOthers": "Others", "imageProcessingError": "Error processing image", "imageSelectionError": "Error selecting image", "categoryTitle": "Category", "week": "week", "month": "month", "year": "year", "costCapNote": "Note: Long-term cost is capped at the item's total value", "netCostCapNote": "Note: Long-term cost is capped at the item's net cost", "capped": "capped", "yearsAgo": " years ago", "monthsAgo": " months ago", "daysAgo": " days ago", "hoursAgo": " hours ago", "minutesAgo": " minutes ago", "justNow": "Just now", "email": "Email", "password": "Password", "pleaseEnterEmail": "Please enter your email", "pleaseEnterValidEmail": "Please enter a valid email address", "pleaseEnterPassword": "Please enter your password", "invalidEmailOrPassword": "Invalid email or password", "loginFailed": "<PERSON><PERSON> failed: ", "offlineLoginWarning": "Network is unavailable, data won't be synced after login", "retry": "Retry", "noAccount": "Don't have an account?", "registerNow": "Register now", "forgotPassword": "Forgot password?", "username": "Username", "pleaseEnterUsername": "Please enter your username", "confirmPassword": "Confirm Password", "pleaseConfirmPassword": "Please confirm your password", "passwordsDoNotMatch": "Passwords do not match", "verificationCode": "Verification Code", "pleaseEnterVerificationCode": "Please enter verification code", "getVerificationCode": "Get Code", "retryAfterSeconds": "Retry in {seconds}s", "@retryAfterSeconds": {"placeholders": {"seconds": {"type": "int"}}}, "verificationCodeSent": "Verification code has been sent to your email", "codeSendingFailed": "Failed to send verification code, email may already be registered", "registrationSuccess": "Registration successful, please login", "registrationFailed": "Registration failed, verification code may be invalid or expired", "passwordMinLength": "Password must be at least 6 characters", "returnToLogin": "Back to login", "alreadyHaveAccount": "Already have an account?", "resetPassword": "Reset Password", "resetPasswordSuccess": "Password reset successful, please login with your new password", "resetPasswordFailed": "Password reset failed, verification code may be invalid or expired", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "pleaseEnterNewPassword": "Please enter new password", "pleaseConfirmNewPassword": "Please confirm new password", "rememberPassword": "Remember your password?", "resetPasswordTitle": "Reset Your Password", "appSlogan": "Smart Cost Tracking for Your Items", "upgradeToUsePremiumDesc": "Backup and restore features are only available to premium users. Upgrade to premium to use cloud backup and restore for more secure data.", "quotaRefreshed": "Available item quota refreshed", "loggedOut": "Logged out successfully", "overview": "Overview", "trends": "Trends", "addItemsToSeeStats": "Add items to see statistics", "addItem": "Add Item", "itemUsageTime": "Item Usage Time", "categoryAnalysis": "Category Analysis", "items": "Items", "costTrends": "Cost Trends", "monthlyExpenditure": "Monthly Expenditure", "costOptimization": "Cost Optimization", "reduceShoppingTip": "Reduce shopping frequency to save about 15% of expenses", "electronicsTip": "Extend electronics usage time to reduce 25% of cost", "investmentTip": "Consider investing in high-value, long-cycle items", "membershipBenefits": "Membership Benefits", "advancedAnalytics": "Advanced Data Analytics", "advancedAnalyticsDesc": "Unlock detailed spending trend charts and smart consumption recommendations", "cloudBackup": "Cloud Backup", "cloudBackupDesc": "Automatically protect your important data, never lose it", "multiDeviceSync": "Multi-device Sync", "multiDeviceSyncDesc": "Seamlessly switch between phone, tablet, and computer", "unlimitedItems": "Unlimited Items", "unlimitedItemsDesc": "Break through the 30-item limit, add all your valuable items", "customThemes": "Custom Themes", "customThemesDesc": "Personalized interface and more visual options", "adFreeExperience": "Ad-Free Experience", "adFreeExperienceDesc": "Focus on using the app without any distractions", "chooseMembershipPlan": "Choose Your Membership Plan", "lifetimeMembership": "Lifetime", "lifetimeMembershipDesc": "Pay once, enjoy forever - All premium features permanently unlocked, most economical for long-term use", "annualMembership": "Annual", "annualMembershipDesc": "Annual plan saves more - Unlock all premium features for a year of worry-free experience", "monthlyMembership": "Monthly", "monthlyMembershipDesc": "Flexible subscription - Start or cancel anytime, easy access to all premium features", "bestValue": "Best Value", "save50": "Save 50%", "buyNow": "Buy Now", "processing": "Processing...", "monthAbbr1": "Jan", "monthAbbr2": "Feb", "monthAbbr3": "Mar", "monthAbbr4": "Apr", "monthAbbr5": "May", "monthAbbr6": "Jun", "monthAbbr7": "Jul", "monthAbbr8": "Aug", "monthAbbr9": "Sep", "monthAbbr10": "Oct", "monthAbbr11": "Nov", "monthAbbr12": "Dec", "restorePurchases": "<PERSON><PERSON> Purchases", "restoringPurchases": "Restoring purchases...", "purchaseRestoreSuccess": "Purchases restored successfully", "purchaseRestoreFailed": "Failed to restore purchases", "transferDateTitle": "Transfer Date", "damageDate": "Damage Date", "inactiveDate": "Inactive Date", "statusDateDescription": "Please select the date when the status changed. This will be used to calculate the actual usage cost of the item.", "selectDate": "Select Date", "useCurrentDate": "Use Current Date", "itemStatusActiveSuccess": "Item has been set to active status", "itemStatusTransferSuccess": "Item has been transferred, transfer amount: {amount}, end date: {date}", "@itemStatusTransferSuccess": {"placeholders": {"amount": {"type": "String"}, "date": {"type": "String"}}}, "itemStatusChangeSuccess": "Item status has been updated, end date: {date}", "@itemStatusChangeSuccess": {"placeholders": {"date": {"type": "String"}}}, "splashInitializing": "Initializing...", "splashLoading": "Starting up...", "splashWelcome": "Welcome to CostTrack", "splashInitializationComplete": "Ready to go", "splashInitializationFailed": "Startup failed: {error}", "@splashInitializationFailed": {"placeholders": {"error": {"type": "String"}}}}