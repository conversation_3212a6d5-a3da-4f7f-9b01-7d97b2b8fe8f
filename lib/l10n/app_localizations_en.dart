// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'CostTrack';

  @override
  String get login => 'Login';

  @override
  String get register => 'Register';

  @override
  String get settings => 'Settings';

  @override
  String get profile => 'Profile';

  @override
  String get membership => 'Membership Service';

  @override
  String get premiumUser => 'Premium User';

  @override
  String get ordinaryUser => 'Ordinary User';

  @override
  String get expiryDate => 'Expires on';

  @override
  String get language => 'Language';

  @override
  String get followSystem => 'Follow System';

  @override
  String get english => 'English';

  @override
  String get chinese => 'Chinese';

  @override
  String get theme => 'Theme';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get lightMode => 'Light Mode';

  @override
  String get systemMode => 'System Default';

  @override
  String get accountAndService => 'Account & Services';

  @override
  String get myData => 'My Assets';

  @override
  String get backupAndRestore => 'Backup & Restore';

  @override
  String get premiumExclusive => 'Premium Exclusive';

  @override
  String get feedback => 'Feedback';

  @override
  String get aboutUs => 'About Us';

  @override
  String get loginRequired => 'Login Required';

  @override
  String get pleaseLoginFirst => 'Please login first';

  @override
  String get cancel => 'Cancel';

  @override
  String get ok => 'OK';

  @override
  String get logout => 'Logout';

  @override
  String get logoutConfirm => 'Are you sure you want to logout?';

  @override
  String avaliableItems(Object count) {
    return 'Available Items: $count/30';
  }

  @override
  String get home => 'Home';

  @override
  String get category => 'Category';

  @override
  String get statistics => 'Statistics';

  @override
  String get noItems => 'You haven\'t added any items yet';

  @override
  String get addFirstItem => 'Add your first item';

  @override
  String get recentlyAdded => 'Recently Added Items';

  @override
  String get highestDailyCost => 'Highest Daily Cost Items';

  @override
  String get noData => 'No data available';

  @override
  String get showAllItems => 'Show all items';

  @override
  String get showActiveItems => 'Show active items only';

  @override
  String get loadDemoData => 'Load demo data';

  @override
  String get userFeedback => 'User Feedback';

  @override
  String get feedbackTip => 'We value your feedback. Please let us know your thoughts or report any issues:';

  @override
  String get feedbackType => 'Feedback Type';

  @override
  String get feedbackContent => 'Feedback Content';

  @override
  String get feedbackContentHint => 'Please describe your suggestion or issue in detail...';

  @override
  String get contactInfo => 'Contact Information (Optional)';

  @override
  String get contactInfoHint => 'You can leave your email or phone number for us to reply';

  @override
  String get submitFeedback => 'Submit Feedback';

  @override
  String get feedbackSuccess => 'Thank you for your feedback, we will process it as soon as possible!';

  @override
  String get anonymousFeedbackTip => 'You are providing anonymous feedback. Sign in for a better experience.';

  @override
  String get feedbackAndSuggestions => 'Feedback and Suggestions';

  @override
  String get noStatsData => 'No statistics data available';

  @override
  String get statisticsInfo => 'Statistics Information';

  @override
  String get aboutUsTitle => 'About Us';

  @override
  String get aboutUsDescription => 'CostTrack is a tool designed to help you track and calculate the usage cost of your items.';

  @override
  String get addItemTitle => 'Add Item';

  @override
  String get itemName => 'Item Name';

  @override
  String get itemCategory => 'Category';

  @override
  String get purchaseDate => 'Purchase Date';

  @override
  String get purchasePrice => 'Purchase Price';

  @override
  String get itemNotes => 'Notes (Optional)';

  @override
  String get saveItem => 'Save Item';

  @override
  String get itemImage => 'Item Image';

  @override
  String get chooseImage => 'Choose Image';

  @override
  String get takePhoto => 'Take Photo';

  @override
  String get loadDemoDataTitle => 'Load Demo Data';

  @override
  String get loadDemoDataConfirm => 'This will clear existing data and load sample data. Continue?';

  @override
  String get demoDataLoadSuccess => 'Sample data loaded successfully!';

  @override
  String get apiModeError => 'Cannot load demo data in API mode!';

  @override
  String get featureSuggestion => 'Feature Suggestion';

  @override
  String get performanceIssue => 'Performance Issue';

  @override
  String get userExperience => 'User Experience';

  @override
  String get membershipService => 'Membership Service';

  @override
  String get otherIssues => 'Other Issues';

  @override
  String get totalCost => 'Total Cost';

  @override
  String get dailyAverage => 'Daily Average';

  @override
  String get itemCount => 'Item Count';

  @override
  String get costDistribution => 'Cost Distribution by Category';

  @override
  String get statusDistribution => 'Status Distribution';

  @override
  String get dataRefreshed => 'Data refreshed';

  @override
  String get refreshData => 'Refresh data';

  @override
  String get itemOverview => 'Item Overview';

  @override
  String get categoryDistribution => 'Category Distribution';

  @override
  String get highestDailyCostItems => 'Highest Daily Cost Items';

  @override
  String get totalItems => 'Total Items';

  @override
  String get totalInvestment => 'Total Investment';

  @override
  String get averageDailyCost => 'Average Daily Cost';

  @override
  String get daysUsed => 'Days Used';

  @override
  String get days => 'days';

  @override
  String get day => 'day';

  @override
  String get editItemTitle => 'Edit Item';

  @override
  String get updateItem => 'Update Item';

  @override
  String get pleaseEnterItemName => 'Please enter item name';

  @override
  String get pleaseEnterPrice => 'Please enter price';

  @override
  String get pleaseEnterValidPrice => 'Please enter a valid price';

  @override
  String get pleaseSelectCategory => 'Please select a category';

  @override
  String get pleaseSelectDate => 'Please select purchase date';

  @override
  String get selectImageSource => 'Select Image Source';

  @override
  String get backupRestoreTitle => 'Data Backup & Restore';

  @override
  String get backupRestoreDescription => 'You can backup your item data to the cloud, or restore your data from the cloud.';

  @override
  String get backupToCloud => 'Backup to Cloud';

  @override
  String get restoreFromCloud => 'Restore from Cloud';

  @override
  String get backupCaution => 'Backup and restore will overwrite existing data, please be cautious';

  @override
  String get close => 'Close';

  @override
  String get processingImage => 'Processing image...';

  @override
  String get networkUnavailable => 'Network connection unavailable, please check your network settings';

  @override
  String get userInfoError => 'Unable to get user information, please log in again';

  @override
  String get noItemsToBackup => 'No items need to be backed up';

  @override
  String get gettingCloudData => 'Getting cloud data...';

  @override
  String getCloudDataFailed(String error) {
    return 'Failed to get cloud data: $error';
  }

  @override
  String get deletingCloudItems => 'Deleting unnecessary items from the cloud...';

  @override
  String deleteCloudItemsFailed(String error) {
    return 'Failed to delete cloud items: $error';
  }

  @override
  String uploadingLocalItems(int current, int total) {
    return 'Uploading local items to the cloud ($current/$total)...';
  }

  @override
  String partialUploadFailed(int success, int failed, int deleted) {
    return 'Some items failed to upload, synchronized $success items, failed $failed items, deleted $deleted items';
  }

  @override
  String backupSuccess(int added, int deleted) {
    return 'Data backup succeeded! Synchronized $added items, deleted $deleted items';
  }

  @override
  String updatingItems(int current, int total) {
    return 'Updating items ($current/$total)...';
  }

  @override
  String backupSuccessWithUpdate(int added, int deleted, int updated) {
    return 'Data backup succeeded! Synchronized $added items, deleted $deleted items, updated $updated items';
  }

  @override
  String backupError(String error) {
    return 'Error during backup: $error';
  }

  @override
  String restoreSuccess(int count) {
    return 'Successfully restored $count items';
  }

  @override
  String restoreFailed(String error) {
    return 'Restore failed: $error';
  }

  @override
  String restoreError(String error) {
    return 'Error during restore: $error';
  }

  @override
  String get premiumFeature => 'Premium Feature';

  @override
  String get upgradeToUsePremium => 'Backup and restore features are only available to premium users. Upgrade to premium to use cloud backup and restore, making your data more secure.';

  @override
  String get upgradeToMember => 'Upgrade Membership';

  @override
  String get notNow => 'Not Now';

  @override
  String get itemLimitTitle => 'Item limit reached';

  @override
  String get itemLimitDescription => 'Free users can add up to 30 items. Please upgrade to premium to unlock unlimited item addition and cloud synchronization services.';

  @override
  String get lateTalk => 'Maybe Later';

  @override
  String categoryLabel(String category) {
    return 'Category: $category';
  }

  @override
  String get basicInfo => 'Basic Information';

  @override
  String usageDays(int days) {
    return '$days days';
  }

  @override
  String get costAnalysis => 'Cost Analysis';

  @override
  String get netCost => 'Net Cost';

  @override
  String get dailyCost => 'Daily Cost';

  @override
  String weeklyCost(String cost) {
    return '$cost/week';
  }

  @override
  String monthlyCost(String cost) {
    return '$cost/month';
  }

  @override
  String yearlyCost(String cost) {
    return '$cost/year';
  }

  @override
  String costCap(String value) {
    return 'Note: Long-term cost is capped at the item\'s total value ($value)';
  }

  @override
  String netCostCap(String value) {
    return 'Note: Long-term cost is capped at the item\'s net cost ($value)';
  }

  @override
  String get notes => 'Notes';

  @override
  String get itemStatus => 'Item Status';

  @override
  String get changeStatus => 'Change Status';

  @override
  String get statusActive => 'Active';

  @override
  String get statusDamaged => 'Damaged';

  @override
  String get statusInactive => 'Inactive';

  @override
  String get statusTransfer => 'Transferred';

  @override
  String get statusDescription => 'Status Description:\nActive - Item is normally counted in daily cost\nDamaged - Item is damaged and no longer counted in cost\nInactive - Item is no longer in use and not counted in cost\nTransferred - Item has been transferred to others and not counted in cost';

  @override
  String get confirmDelete => 'Confirm Delete';

  @override
  String deleteConfirmation(String name) {
    return 'Are you sure you want to delete \"$name\"? This action cannot be undone.';
  }

  @override
  String get delete => 'Delete';

  @override
  String get transferItem => 'Transfer Item';

  @override
  String get transferDescription => 'Please enter the amount received when transferring out, this will help calculate the actual cost of the item.';

  @override
  String get transferAmount => 'Transfer Amount';

  @override
  String get transferNote => 'After transfer, the item will automatically be set to \"Inactive\" status and no longer counted in daily cost.';

  @override
  String get confirmTransfer => 'Confirm Transfer';

  @override
  String get pleaseEnterTransferAmount => 'Please enter transfer amount';

  @override
  String get pleaseEnterValidTransferAmount => 'Please enter a valid transfer amount';

  @override
  String get imageNotFound => 'Image Not Found';

  @override
  String get cannotLoadImage => 'Cannot Load Image';

  @override
  String get categoryElectronics => 'Electronics';

  @override
  String get categoryMobile => 'Mobile';

  @override
  String get categoryComputer => 'Computer';

  @override
  String get categoryHousehold => 'Household';

  @override
  String get categoryKitchen => 'Kitchen';

  @override
  String get categoryBooks => 'Books';

  @override
  String get categoryClothing => 'Clothing';

  @override
  String get categorySports => 'Sports';

  @override
  String get categoryToys => 'Toys';

  @override
  String get categoryOffice => 'Office';

  @override
  String get categoryOthers => 'Others';

  @override
  String get imageProcessingError => 'Error processing image';

  @override
  String get imageSelectionError => 'Error selecting image';

  @override
  String get categoryTitle => 'Category';

  @override
  String get week => 'week';

  @override
  String get month => 'month';

  @override
  String get year => 'year';

  @override
  String get costCapNote => 'Note: Long-term cost is capped at the item\'s total value';

  @override
  String get netCostCapNote => 'Note: Long-term cost is capped at the item\'s net cost';

  @override
  String get capped => 'capped';

  @override
  String get yearsAgo => ' years ago';

  @override
  String get monthsAgo => ' months ago';

  @override
  String get daysAgo => ' days ago';

  @override
  String get hoursAgo => ' hours ago';

  @override
  String get minutesAgo => ' minutes ago';

  @override
  String get justNow => 'Just now';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get pleaseEnterEmail => 'Please enter your email';

  @override
  String get pleaseEnterValidEmail => 'Please enter a valid email address';

  @override
  String get pleaseEnterPassword => 'Please enter your password';

  @override
  String get invalidEmailOrPassword => 'Invalid email or password';

  @override
  String get loginFailed => 'Login failed: ';

  @override
  String get offlineLoginWarning => 'Network is unavailable, data won\'t be synced after login';

  @override
  String get retry => 'Retry';

  @override
  String get noAccount => 'Don\'t have an account?';

  @override
  String get registerNow => 'Register now';

  @override
  String get forgotPassword => 'Forgot password?';

  @override
  String get username => 'Username';

  @override
  String get pleaseEnterUsername => 'Please enter your username';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get pleaseConfirmPassword => 'Please confirm your password';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get verificationCode => 'Verification Code';

  @override
  String get pleaseEnterVerificationCode => 'Please enter verification code';

  @override
  String get getVerificationCode => 'Get Code';

  @override
  String retryAfterSeconds(int seconds) {
    return 'Retry in ${seconds}s';
  }

  @override
  String get verificationCodeSent => 'Verification code has been sent to your email';

  @override
  String get codeSendingFailed => 'Failed to send verification code, email may already be registered';

  @override
  String get registrationSuccess => 'Registration successful, please login';

  @override
  String get registrationFailed => 'Registration failed, verification code may be invalid or expired';

  @override
  String get passwordMinLength => 'Password must be at least 6 characters';

  @override
  String get returnToLogin => 'Back to login';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get resetPasswordSuccess => 'Password reset successful, please login with your new password';

  @override
  String get resetPasswordFailed => 'Password reset failed, verification code may be invalid or expired';

  @override
  String get newPassword => 'New Password';

  @override
  String get confirmNewPassword => 'Confirm New Password';

  @override
  String get pleaseEnterNewPassword => 'Please enter new password';

  @override
  String get pleaseConfirmNewPassword => 'Please confirm new password';

  @override
  String get rememberPassword => 'Remember your password?';

  @override
  String get resetPasswordTitle => 'Reset Your Password';

  @override
  String get appSlogan => 'Smart Cost Tracking for Your Items';

  @override
  String get upgradeToUsePremiumDesc => 'Backup and restore features are only available to premium users. Upgrade to premium to use cloud backup and restore for more secure data.';

  @override
  String get quotaRefreshed => 'Available item quota refreshed';

  @override
  String get loggedOut => 'Logged out successfully';

  @override
  String get overview => 'Overview';

  @override
  String get trends => 'Trends';

  @override
  String get addItemsToSeeStats => 'Add items to see statistics';

  @override
  String get addItem => 'Add Item';

  @override
  String get itemUsageTime => 'Item Usage Time';

  @override
  String get categoryAnalysis => 'Category Analysis';

  @override
  String get items => 'Items';

  @override
  String get costTrends => 'Cost Trends';

  @override
  String get monthlyExpenditure => 'Monthly Expenditure';

  @override
  String get costOptimization => 'Cost Optimization';

  @override
  String get reduceShoppingTip => 'Reduce shopping frequency to save about 15% of expenses';

  @override
  String get electronicsTip => 'Extend electronics usage time to reduce 25% of cost';

  @override
  String get investmentTip => 'Consider investing in high-value, long-cycle items';

  @override
  String get membershipBenefits => 'Membership Benefits';

  @override
  String get advancedAnalytics => 'Advanced Data Analytics';

  @override
  String get advancedAnalyticsDesc => 'Unlock detailed spending trend charts and smart consumption recommendations';

  @override
  String get cloudBackup => 'Cloud Backup';

  @override
  String get cloudBackupDesc => 'Automatically protect your important data, never lose it';

  @override
  String get multiDeviceSync => 'Multi-device Sync';

  @override
  String get multiDeviceSyncDesc => 'Seamlessly switch between phone, tablet, and computer';

  @override
  String get unlimitedItems => 'Unlimited Items';

  @override
  String get unlimitedItemsDesc => 'Break through the 30-item limit, add all your valuable items';

  @override
  String get customThemes => 'Custom Themes';

  @override
  String get customThemesDesc => 'Personalized interface and more visual options';

  @override
  String get adFreeExperience => 'Ad-Free Experience';

  @override
  String get adFreeExperienceDesc => 'Focus on using the app without any distractions';

  @override
  String get chooseMembershipPlan => 'Choose Your Membership Plan';

  @override
  String get lifetimeMembership => 'Lifetime';

  @override
  String get lifetimeMembershipDesc => 'Pay once, enjoy forever - All premium features permanently unlocked, most economical for long-term use';

  @override
  String get annualMembership => 'Annual';

  @override
  String get annualMembershipDesc => 'Annual plan saves more - Unlock all premium features for a year of worry-free experience';

  @override
  String get monthlyMembership => 'Monthly';

  @override
  String get monthlyMembershipDesc => 'Flexible subscription - Start or cancel anytime, easy access to all premium features';

  @override
  String get bestValue => 'Best Value';

  @override
  String get save50 => 'Save 50%';

  @override
  String get buyNow => 'Buy Now';

  @override
  String get processing => 'Processing...';

  @override
  String get monthAbbr1 => 'Jan';

  @override
  String get monthAbbr2 => 'Feb';

  @override
  String get monthAbbr3 => 'Mar';

  @override
  String get monthAbbr4 => 'Apr';

  @override
  String get monthAbbr5 => 'May';

  @override
  String get monthAbbr6 => 'Jun';

  @override
  String get monthAbbr7 => 'Jul';

  @override
  String get monthAbbr8 => 'Aug';

  @override
  String get monthAbbr9 => 'Sep';

  @override
  String get monthAbbr10 => 'Oct';

  @override
  String get monthAbbr11 => 'Nov';

  @override
  String get monthAbbr12 => 'Dec';

  @override
  String get restorePurchases => 'Restore Purchases';

  @override
  String get restoringPurchases => 'Restoring purchases...';

  @override
  String get purchaseRestoreSuccess => 'Purchases restored successfully';

  @override
  String get purchaseRestoreFailed => 'Failed to restore purchases';

  @override
  String get transferDateTitle => 'Transfer Date';

  @override
  String get damageDate => 'Damage Date';

  @override
  String get inactiveDate => 'Inactive Date';

  @override
  String get statusDateDescription => 'Please select the date when the status changed. This will be used to calculate the actual usage cost of the item.';

  @override
  String get selectDate => 'Select Date';

  @override
  String get useCurrentDate => 'Use Current Date';

  @override
  String get itemStatusActiveSuccess => 'Item has been set to active status';

  @override
  String itemStatusTransferSuccess(String amount, String date) {
    return 'Item has been transferred, transfer amount: $amount, end date: $date';
  }

  @override
  String itemStatusChangeSuccess(String date) {
    return 'Item status has been updated, end date: $date';
  }

  @override
  String get splashInitializing => 'Initializing...';

  @override
  String get splashLoading => 'Starting up...';

  @override
  String get splashWelcome => 'Welcome to CostTrack';

  @override
  String get splashInitializationComplete => 'Ready to go';

  @override
  String splashInitializationFailed(String error) {
    return 'Startup failed: $error';
  }
}
