// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => '成本追踪';

  @override
  String get login => '登录';

  @override
  String get register => '注册';

  @override
  String get settings => '设置';

  @override
  String get profile => '个人中心';

  @override
  String get membership => '会员服务';

  @override
  String get premiumUser => '高级用户';

  @override
  String get ordinaryUser => '普通用户';

  @override
  String get expiryDate => '到期日';

  @override
  String get language => '语言';

  @override
  String get followSystem => '跟随系统';

  @override
  String get english => '英文';

  @override
  String get chinese => '中文';

  @override
  String get theme => '主题';

  @override
  String get darkMode => '深色模式';

  @override
  String get lightMode => '浅色模式';

  @override
  String get systemMode => '系统默认';

  @override
  String get accountAndService => '账号与服务';

  @override
  String get myData => '我的资产';

  @override
  String get backupAndRestore => '备份与恢复';

  @override
  String get premiumExclusive => '会员专属功能';

  @override
  String get feedback => '意见反馈';

  @override
  String get aboutUs => '关于我们';

  @override
  String get loginRequired => '需要登录';

  @override
  String get pleaseLoginFirst => '请先登录';

  @override
  String get cancel => '取消';

  @override
  String get ok => '确定';

  @override
  String get logout => '退出登录';

  @override
  String get logoutConfirm => '确定要退出登录吗？';

  @override
  String avaliableItems(Object count) {
    return '可用物品: $count/30';
  }

  @override
  String get home => '首页';

  @override
  String get category => '分类';

  @override
  String get statistics => '统计';

  @override
  String get noItems => '您还没有添加任何物品';

  @override
  String get addFirstItem => '添加您的第一个物品';

  @override
  String get recentlyAdded => '最近添加的物品';

  @override
  String get highestDailyCost => '每日成本最高的物品';

  @override
  String get noData => '暂无数据';

  @override
  String get showAllItems => '显示所有物品';

  @override
  String get showActiveItems => '仅显示使用中物品';

  @override
  String get loadDemoData => '加载演示数据';

  @override
  String get userFeedback => '用户反馈';

  @override
  String get feedbackTip => '我们重视您的反馈。请告诉我们您的想法或报告任何问题：';

  @override
  String get feedbackType => '反馈类型';

  @override
  String get feedbackContent => '反馈内容';

  @override
  String get feedbackContentHint => '请详细描述您的建议或问题...';

  @override
  String get contactInfo => '联系方式（选填）';

  @override
  String get contactInfoHint => '您可以留下邮箱或电话号码以便我们回复';

  @override
  String get submitFeedback => '提交反馈';

  @override
  String get feedbackSuccess => '感谢您的反馈，我们会尽快处理！';

  @override
  String get anonymousFeedbackTip => '您正在进行匿名反馈。登录后可获得更好的体验。';

  @override
  String get feedbackAndSuggestions => '意见与建议';

  @override
  String get noStatsData => '没有可用的统计数据';

  @override
  String get statisticsInfo => '统计信息';

  @override
  String get aboutUsTitle => '关于我们';

  @override
  String get aboutUsDescription => 'CostTrack是一款帮助您追踪和计算物品使用成本的工具。';

  @override
  String get addItemTitle => '添加物品';

  @override
  String get itemName => '物品名称';

  @override
  String get itemCategory => '物品分类';

  @override
  String get purchaseDate => '购买日期';

  @override
  String get purchasePrice => '购买价格';

  @override
  String get itemNotes => '备注（选填）';

  @override
  String get saveItem => '保存物品';

  @override
  String get itemImage => '物品图片';

  @override
  String get chooseImage => '选择图片';

  @override
  String get takePhoto => '拍照';

  @override
  String get loadDemoDataTitle => '加载演示数据';

  @override
  String get loadDemoDataConfirm => '这将清除现有数据并加载示例数据。是否继续？';

  @override
  String get demoDataLoadSuccess => '示例数据加载成功！';

  @override
  String get apiModeError => 'API模式下无法加载演示数据！';

  @override
  String get featureSuggestion => '功能建议';

  @override
  String get performanceIssue => '性能问题';

  @override
  String get userExperience => '用户体验';

  @override
  String get membershipService => '会员服务';

  @override
  String get otherIssues => '其他问题';

  @override
  String get totalCost => '总成本';

  @override
  String get dailyAverage => '日均成本';

  @override
  String get itemCount => '物品数量';

  @override
  String get costDistribution => '各类别成本分布';

  @override
  String get statusDistribution => '状态分布';

  @override
  String get dataRefreshed => '数据已刷新';

  @override
  String get refreshData => '刷新数据';

  @override
  String get itemOverview => '物品概览';

  @override
  String get categoryDistribution => '分类分布';

  @override
  String get highestDailyCostItems => '每日成本最高物品';

  @override
  String get totalItems => '总物品数';

  @override
  String get totalInvestment => '总投资额';

  @override
  String get averageDailyCost => '平均日均成本';

  @override
  String get daysUsed => '使用天数';

  @override
  String get days => '天';

  @override
  String get day => '天';

  @override
  String get editItemTitle => '编辑物品';

  @override
  String get updateItem => '更新物品';

  @override
  String get pleaseEnterItemName => '请输入物品名称';

  @override
  String get pleaseEnterPrice => '请输入价格';

  @override
  String get pleaseEnterValidPrice => '请输入有效的价格';

  @override
  String get pleaseSelectCategory => '请选择分类';

  @override
  String get pleaseSelectDate => '请选择购买日期';

  @override
  String get selectImageSource => '选择图片来源';

  @override
  String get backupRestoreTitle => '数据备份与恢复';

  @override
  String get backupRestoreDescription => '您可以将物品数据备份到云端，或从云端恢复数据';

  @override
  String get backupToCloud => '备份到云端';

  @override
  String get restoreFromCloud => '从云端恢复';

  @override
  String get backupCaution => '备份和恢复将覆盖现有数据，请谨慎操作';

  @override
  String get close => '关闭';

  @override
  String get processingImage => '正在处理图片...';

  @override
  String get networkUnavailable => '网络连接不可用，请检查网络设置';

  @override
  String get userInfoError => '无法获取用户信息，请重新登录';

  @override
  String get noItemsToBackup => '没有需要备份的物品';

  @override
  String get gettingCloudData => '正在获取云端数据...';

  @override
  String getCloudDataFailed(String error) {
    return '获取云端数据失败: $error';
  }

  @override
  String get deletingCloudItems => '正在从云端删除无用物品...';

  @override
  String deleteCloudItemsFailed(String error) {
    return '删除云端物品失败: $error';
  }

  @override
  String uploadingLocalItems(int current, int total) {
    return '正在上传本地物品到云端 ($current/$total)...';
  }

  @override
  String partialUploadFailed(int success, int failed, int deleted) {
    return '部分物品上传失败，已同步 $success 个物品，失败 $failed 个物品，删除 $deleted 个物品';
  }

  @override
  String backupSuccess(int added, int deleted) {
    return '数据备份成功！已同步 $added 个物品，删除 $deleted 个物品';
  }

  @override
  String updatingItems(int current, int total) {
    return '正在更新物品 ($current/$total)...';
  }

  @override
  String backupSuccessWithUpdate(int added, int deleted, int updated) {
    return '数据备份成功！已同步 $added 个物品，删除 $deleted 个物品，更新 $updated 个物品';
  }

  @override
  String backupError(String error) {
    return '备份过程中出错: $error';
  }

  @override
  String restoreSuccess(int count) {
    return '成功恢复 $count 个物品';
  }

  @override
  String restoreFailed(String error) {
    return '恢复失败: $error';
  }

  @override
  String restoreError(String error) {
    return '恢复过程中出错: $error';
  }

  @override
  String get premiumFeature => '高级功能';

  @override
  String get upgradeToUsePremium => '备份和还原功能仅适用于高级会员。升级为高级会员以使用云备份和还原功能，使您的数据更安全。';

  @override
  String get upgradeToMember => '升级会员';

  @override
  String get notNow => '暂不升级';

  @override
  String get itemLimitTitle => '物品限制已达上限';

  @override
  String get itemLimitDescription => '免费用户最多可添加30个物品。请升级为高级会员以解锁无限物品添加和云同步服务。';

  @override
  String get lateTalk => '稍后再说';

  @override
  String categoryLabel(String category) {
    return '分类: $category';
  }

  @override
  String get basicInfo => '基本信息';

  @override
  String usageDays(int days) {
    return '$days 天';
  }

  @override
  String get costAnalysis => '成本分析';

  @override
  String get netCost => '净成本';

  @override
  String get dailyCost => '每日成本';

  @override
  String weeklyCost(String cost) {
    return '$cost/周';
  }

  @override
  String monthlyCost(String cost) {
    return '$cost/月';
  }

  @override
  String yearlyCost(String cost) {
    return '$cost/年';
  }

  @override
  String costCap(String value) {
    return '注意：长期成本上限为物品总价值（$value）';
  }

  @override
  String netCostCap(String value) {
    return '注意：长期成本上限为物品净成本（$value）';
  }

  @override
  String get notes => '备注';

  @override
  String get itemStatus => '物品状态';

  @override
  String get changeStatus => '修改物品状态';

  @override
  String get statusActive => '正常使用中';

  @override
  String get statusDamaged => '已损坏';

  @override
  String get statusInactive => '已停用';

  @override
  String get statusTransfer => '已转让';

  @override
  String get statusDescription => '状态说明：\n正常使用中 - 物品正常计入每日成本\n已损坏 - 物品已损坏，不再计入成本\n已停用 - 物品已不再使用，不再计入成本\n已转让 - 物品已转让给他人，不再计入成本';

  @override
  String get confirmDelete => '确认删除';

  @override
  String deleteConfirmation(String name) {
    return '您确定要删除 \"$name\" 吗？此操作不可撤销。';
  }

  @override
  String get delete => '删除';

  @override
  String get transferItem => '物品转出';

  @override
  String get transferDescription => '请输入转出时收到的金额，这将帮助计算物品的实际成本。';

  @override
  String get transferAmount => '转出金额';

  @override
  String get transferNote => '转出后物品将自动设为\"已停用\"状态，不再计入日常成本。';

  @override
  String get confirmTransfer => '确认转出';

  @override
  String get pleaseEnterTransferAmount => '请输入转出金额';

  @override
  String get pleaseEnterValidTransferAmount => '请输入有效的转出金额';

  @override
  String get imageNotFound => '图片未找到';

  @override
  String get cannotLoadImage => '无法加载图片';

  @override
  String get categoryElectronics => '电子产品';

  @override
  String get categoryMobile => '手机';

  @override
  String get categoryComputer => '电脑';

  @override
  String get categoryHousehold => '家居用品';

  @override
  String get categoryKitchen => '厨房用品';

  @override
  String get categoryBooks => '书籍';

  @override
  String get categoryClothing => '衣物';

  @override
  String get categorySports => '运动用品';

  @override
  String get categoryToys => '玩具';

  @override
  String get categoryOffice => '办公用品';

  @override
  String get categoryOthers => '其它';

  @override
  String get imageProcessingError => '处理图片时出错';

  @override
  String get imageSelectionError => '选择图片时出错';

  @override
  String get categoryTitle => '分类';

  @override
  String get week => '周';

  @override
  String get month => '月';

  @override
  String get year => '年';

  @override
  String get costCapNote => '注：长期成本已按物品总价值';

  @override
  String get netCostCapNote => '注：长期成本已按物品净成本';

  @override
  String get capped => '封顶';

  @override
  String get yearsAgo => '年前';

  @override
  String get monthsAgo => '个月前';

  @override
  String get daysAgo => '天前';

  @override
  String get hoursAgo => '小时前';

  @override
  String get minutesAgo => '分钟前';

  @override
  String get justNow => '刚刚';

  @override
  String get email => '邮箱';

  @override
  String get password => '密码';

  @override
  String get pleaseEnterEmail => '请输入邮箱';

  @override
  String get pleaseEnterValidEmail => '请输入有效的邮箱地址';

  @override
  String get pleaseEnterPassword => '请输入密码';

  @override
  String get invalidEmailOrPassword => '邮箱或密码不正确';

  @override
  String get loginFailed => '登录失败: ';

  @override
  String get offlineLoginWarning => '当前网络不可用，登录后将无法同步数据';

  @override
  String get retry => '重试';

  @override
  String get noAccount => '还没有账号？';

  @override
  String get registerNow => '立即注册';

  @override
  String get forgotPassword => '忘记密码？';

  @override
  String get username => '用户名';

  @override
  String get pleaseEnterUsername => '请输入用户名';

  @override
  String get confirmPassword => '确认密码';

  @override
  String get pleaseConfirmPassword => '请确认密码';

  @override
  String get passwordsDoNotMatch => '两次输入的密码不一致';

  @override
  String get verificationCode => '验证码';

  @override
  String get pleaseEnterVerificationCode => '请输入验证码';

  @override
  String get getVerificationCode => '获取验证码';

  @override
  String retryAfterSeconds(int seconds) {
    return '$seconds秒后重试';
  }

  @override
  String get verificationCodeSent => '验证码已发送到邮箱，请查收';

  @override
  String get codeSendingFailed => '验证码发送失败，邮箱可能已被注册';

  @override
  String get registrationSuccess => '注册成功，请登录';

  @override
  String get registrationFailed => '注册失败，验证码可能无效或已过期';

  @override
  String get passwordMinLength => '密码长度不能少于6个字符';

  @override
  String get returnToLogin => '返回登录';

  @override
  String get alreadyHaveAccount => '已有账号？';

  @override
  String get resetPassword => '重置密码';

  @override
  String get resetPasswordSuccess => '密码重置成功，请使用新密码登录';

  @override
  String get resetPasswordFailed => '密码重置失败，验证码可能无效或已过期';

  @override
  String get newPassword => '新密码';

  @override
  String get confirmNewPassword => '确认新密码';

  @override
  String get pleaseEnterNewPassword => '请输入新密码';

  @override
  String get pleaseConfirmNewPassword => '请确认新密码';

  @override
  String get rememberPassword => '记起密码了？';

  @override
  String get resetPasswordTitle => '重置您的密码';

  @override
  String get appSlogan => '智能追踪您的物品成本';

  @override
  String get upgradeToUsePremiumDesc => '备份与恢复功能仅对会员用户开放。升级为会员即可使用云端备份与恢复功能，让您的数据更安全。';

  @override
  String get quotaRefreshed => '已刷新可添加物品数量';

  @override
  String get loggedOut => '已退出登录';

  @override
  String get overview => '概览';

  @override
  String get trends => '趋势';

  @override
  String get addItemsToSeeStats => '添加物品后即可查看统计信息';

  @override
  String get addItem => '添加物品';

  @override
  String get itemUsageTime => '物品使用时间';

  @override
  String get categoryAnalysis => '分类分析';

  @override
  String get items => '物品';

  @override
  String get costTrends => '成本趋势';

  @override
  String get monthlyExpenditure => '月度支出';

  @override
  String get costOptimization => '成本优化';

  @override
  String get reduceShoppingTip => '减少购物频率可节省约15%的支出';

  @override
  String get electronicsTip => '延长电子产品使用时间可减少25%成本';

  @override
  String get investmentTip => '考虑投资于高价值、长周期的物品';

  @override
  String get membershipBenefits => '会员权益';

  @override
  String get advancedAnalytics => '高级数据分析';

  @override
  String get advancedAnalyticsDesc => '解锁详尽的消费趋势图表和智能消费建议';

  @override
  String get cloudBackup => '云端安全备份';

  @override
  String get cloudBackupDesc => '自动保护您的重要数据，永不丢失';

  @override
  String get multiDeviceSync => '多设备同步';

  @override
  String get multiDeviceSyncDesc => '在手机、平板和电脑间无缝切换使用';

  @override
  String get unlimitedItems => '无限物品记录';

  @override
  String get unlimitedItemsDesc => '突破30个物品限制，添加您所有的珍贵物品';

  @override
  String get customThemes => '专属主题与定制';

  @override
  String get customThemesDesc => '个性化界面和更多视觉选项';

  @override
  String get adFreeExperience => '纯净无广告体验';

  @override
  String get adFreeExperienceDesc => '专注于使用，不受任何干扰';

  @override
  String get chooseMembershipPlan => '选择适合您的会员方案';

  @override
  String get lifetimeMembership => '永久会员';

  @override
  String get lifetimeMembershipDesc => '一次付费，终身享有 - 所有高级功能永久解锁，长期使用最经济实惠';

  @override
  String get annualMembership => '年度会员';

  @override
  String get annualMembershipDesc => '年付更划算 - 解锁完整高级功能，享受全年无忧的高效体验';

  @override
  String get monthlyMembership => '月度会员';

  @override
  String get monthlyMembershipDesc => '灵活订阅 - 随时开始或取消，轻松体验所有高级功能';

  @override
  String get bestValue => '超值优惠';

  @override
  String get save50 => '省50%';

  @override
  String get buyNow => '立即购买';

  @override
  String get processing => '处理中...';

  @override
  String get monthAbbr1 => '1月';

  @override
  String get monthAbbr2 => '2月';

  @override
  String get monthAbbr3 => '3月';

  @override
  String get monthAbbr4 => '4月';

  @override
  String get monthAbbr5 => '5月';

  @override
  String get monthAbbr6 => '6月';

  @override
  String get monthAbbr7 => '7月';

  @override
  String get monthAbbr8 => '8月';

  @override
  String get monthAbbr9 => '9月';

  @override
  String get monthAbbr10 => '10月';

  @override
  String get monthAbbr11 => '11月';

  @override
  String get monthAbbr12 => '12月';

  @override
  String get restorePurchases => '恢复购买';

  @override
  String get restoringPurchases => '正在恢复购买...';

  @override
  String get purchaseRestoreSuccess => '购买恢复成功';

  @override
  String get purchaseRestoreFailed => '恢复购买失败';

  @override
  String get transferDateTitle => '转让日期';

  @override
  String get damageDate => '损坏日期';

  @override
  String get inactiveDate => '停用日期';

  @override
  String get statusDateDescription => '请选择状态变更的日期，这将用于计算物品的实际使用成本。';

  @override
  String get selectDate => '选择日期';

  @override
  String get useCurrentDate => '使用当前日期';

  @override
  String get itemStatusActiveSuccess => '物品已设为正常使用状态';

  @override
  String itemStatusTransferSuccess(String amount, String date) {
    return '物品已转让，转让金额：$amount，使用结束日期：$date';
  }

  @override
  String itemStatusChangeSuccess(String date) {
    return '物品状态已更新，使用结束日期：$date';
  }

  @override
  String get splashInitializing => '正在初始化...';

  @override
  String get splashLoading => '正在启动...';

  @override
  String get splashWelcome => '欢迎使用 CostTrack';

  @override
  String get splashInitializationComplete => '启动完成';

  @override
  String splashInitializationFailed(String error) {
    return '启动失败: $error';
  }
}
