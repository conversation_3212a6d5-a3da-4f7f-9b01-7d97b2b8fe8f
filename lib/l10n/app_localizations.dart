import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('zh')
  ];

  /// No description provided for @appTitle.
  ///
  /// In en, this message translates to:
  /// **'CostTrack'**
  String get appTitle;

  /// No description provided for @login.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// No description provided for @register.
  ///
  /// In en, this message translates to:
  /// **'Register'**
  String get register;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @membership.
  ///
  /// In en, this message translates to:
  /// **'Membership Service'**
  String get membership;

  /// No description provided for @premiumUser.
  ///
  /// In en, this message translates to:
  /// **'Premium User'**
  String get premiumUser;

  /// No description provided for @ordinaryUser.
  ///
  /// In en, this message translates to:
  /// **'Ordinary User'**
  String get ordinaryUser;

  /// No description provided for @expiryDate.
  ///
  /// In en, this message translates to:
  /// **'Expires on'**
  String get expiryDate;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @followSystem.
  ///
  /// In en, this message translates to:
  /// **'Follow System'**
  String get followSystem;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @chinese.
  ///
  /// In en, this message translates to:
  /// **'Chinese'**
  String get chinese;

  /// No description provided for @theme.
  ///
  /// In en, this message translates to:
  /// **'Theme'**
  String get theme;

  /// No description provided for @darkMode.
  ///
  /// In en, this message translates to:
  /// **'Dark Mode'**
  String get darkMode;

  /// No description provided for @lightMode.
  ///
  /// In en, this message translates to:
  /// **'Light Mode'**
  String get lightMode;

  /// No description provided for @systemMode.
  ///
  /// In en, this message translates to:
  /// **'System Default'**
  String get systemMode;

  /// No description provided for @accountAndService.
  ///
  /// In en, this message translates to:
  /// **'Account & Services'**
  String get accountAndService;

  /// No description provided for @myData.
  ///
  /// In en, this message translates to:
  /// **'My Assets'**
  String get myData;

  /// No description provided for @backupAndRestore.
  ///
  /// In en, this message translates to:
  /// **'Backup & Restore'**
  String get backupAndRestore;

  /// No description provided for @premiumExclusive.
  ///
  /// In en, this message translates to:
  /// **'Premium Exclusive'**
  String get premiumExclusive;

  /// No description provided for @feedback.
  ///
  /// In en, this message translates to:
  /// **'Feedback'**
  String get feedback;

  /// No description provided for @aboutUs.
  ///
  /// In en, this message translates to:
  /// **'About Us'**
  String get aboutUs;

  /// No description provided for @loginRequired.
  ///
  /// In en, this message translates to:
  /// **'Login Required'**
  String get loginRequired;

  /// No description provided for @pleaseLoginFirst.
  ///
  /// In en, this message translates to:
  /// **'Please login first'**
  String get pleaseLoginFirst;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// No description provided for @logoutConfirm.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to logout?'**
  String get logoutConfirm;

  /// No description provided for @avaliableItems.
  ///
  /// In en, this message translates to:
  /// **'Available Items: {count}/30'**
  String avaliableItems(Object count);

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @category.
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get category;

  /// No description provided for @statistics.
  ///
  /// In en, this message translates to:
  /// **'Statistics'**
  String get statistics;

  /// No description provided for @noItems.
  ///
  /// In en, this message translates to:
  /// **'You haven\'t added any items yet'**
  String get noItems;

  /// No description provided for @addFirstItem.
  ///
  /// In en, this message translates to:
  /// **'Add your first item'**
  String get addFirstItem;

  /// No description provided for @recentlyAdded.
  ///
  /// In en, this message translates to:
  /// **'Recently Added Items'**
  String get recentlyAdded;

  /// No description provided for @highestDailyCost.
  ///
  /// In en, this message translates to:
  /// **'Highest Daily Cost Items'**
  String get highestDailyCost;

  /// No description provided for @noData.
  ///
  /// In en, this message translates to:
  /// **'No data available'**
  String get noData;

  /// No description provided for @showAllItems.
  ///
  /// In en, this message translates to:
  /// **'Show all items'**
  String get showAllItems;

  /// No description provided for @showActiveItems.
  ///
  /// In en, this message translates to:
  /// **'Show active items only'**
  String get showActiveItems;

  /// No description provided for @loadDemoData.
  ///
  /// In en, this message translates to:
  /// **'Load demo data'**
  String get loadDemoData;

  /// No description provided for @userFeedback.
  ///
  /// In en, this message translates to:
  /// **'User Feedback'**
  String get userFeedback;

  /// No description provided for @feedbackTip.
  ///
  /// In en, this message translates to:
  /// **'We value your feedback. Please let us know your thoughts or report any issues:'**
  String get feedbackTip;

  /// No description provided for @feedbackType.
  ///
  /// In en, this message translates to:
  /// **'Feedback Type'**
  String get feedbackType;

  /// No description provided for @feedbackContent.
  ///
  /// In en, this message translates to:
  /// **'Feedback Content'**
  String get feedbackContent;

  /// No description provided for @feedbackContentHint.
  ///
  /// In en, this message translates to:
  /// **'Please describe your suggestion or issue in detail...'**
  String get feedbackContentHint;

  /// No description provided for @contactInfo.
  ///
  /// In en, this message translates to:
  /// **'Contact Information (Optional)'**
  String get contactInfo;

  /// No description provided for @contactInfoHint.
  ///
  /// In en, this message translates to:
  /// **'You can leave your email or phone number for us to reply'**
  String get contactInfoHint;

  /// No description provided for @submitFeedback.
  ///
  /// In en, this message translates to:
  /// **'Submit Feedback'**
  String get submitFeedback;

  /// No description provided for @feedbackSuccess.
  ///
  /// In en, this message translates to:
  /// **'Thank you for your feedback, we will process it as soon as possible!'**
  String get feedbackSuccess;

  /// No description provided for @anonymousFeedbackTip.
  ///
  /// In en, this message translates to:
  /// **'You are providing anonymous feedback. Sign in for a better experience.'**
  String get anonymousFeedbackTip;

  /// No description provided for @feedbackAndSuggestions.
  ///
  /// In en, this message translates to:
  /// **'Feedback and Suggestions'**
  String get feedbackAndSuggestions;

  /// No description provided for @noStatsData.
  ///
  /// In en, this message translates to:
  /// **'No statistics data available'**
  String get noStatsData;

  /// No description provided for @statisticsInfo.
  ///
  /// In en, this message translates to:
  /// **'Statistics Information'**
  String get statisticsInfo;

  /// No description provided for @aboutUsTitle.
  ///
  /// In en, this message translates to:
  /// **'About Us'**
  String get aboutUsTitle;

  /// No description provided for @aboutUsDescription.
  ///
  /// In en, this message translates to:
  /// **'CostTrack is a tool designed to help you track and calculate the usage cost of your items.'**
  String get aboutUsDescription;

  /// No description provided for @addItemTitle.
  ///
  /// In en, this message translates to:
  /// **'Add Item'**
  String get addItemTitle;

  /// No description provided for @itemName.
  ///
  /// In en, this message translates to:
  /// **'Item Name'**
  String get itemName;

  /// No description provided for @itemCategory.
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get itemCategory;

  /// No description provided for @purchaseDate.
  ///
  /// In en, this message translates to:
  /// **'Purchase Date'**
  String get purchaseDate;

  /// No description provided for @purchasePrice.
  ///
  /// In en, this message translates to:
  /// **'Purchase Price'**
  String get purchasePrice;

  /// No description provided for @itemNotes.
  ///
  /// In en, this message translates to:
  /// **'Notes (Optional)'**
  String get itemNotes;

  /// No description provided for @saveItem.
  ///
  /// In en, this message translates to:
  /// **'Save Item'**
  String get saveItem;

  /// No description provided for @itemImage.
  ///
  /// In en, this message translates to:
  /// **'Item Image'**
  String get itemImage;

  /// No description provided for @chooseImage.
  ///
  /// In en, this message translates to:
  /// **'Choose Image'**
  String get chooseImage;

  /// No description provided for @takePhoto.
  ///
  /// In en, this message translates to:
  /// **'Take Photo'**
  String get takePhoto;

  /// No description provided for @loadDemoDataTitle.
  ///
  /// In en, this message translates to:
  /// **'Load Demo Data'**
  String get loadDemoDataTitle;

  /// No description provided for @loadDemoDataConfirm.
  ///
  /// In en, this message translates to:
  /// **'This will clear existing data and load sample data. Continue?'**
  String get loadDemoDataConfirm;

  /// No description provided for @demoDataLoadSuccess.
  ///
  /// In en, this message translates to:
  /// **'Sample data loaded successfully!'**
  String get demoDataLoadSuccess;

  /// No description provided for @apiModeError.
  ///
  /// In en, this message translates to:
  /// **'Cannot load demo data in API mode!'**
  String get apiModeError;

  /// No description provided for @featureSuggestion.
  ///
  /// In en, this message translates to:
  /// **'Feature Suggestion'**
  String get featureSuggestion;

  /// No description provided for @performanceIssue.
  ///
  /// In en, this message translates to:
  /// **'Performance Issue'**
  String get performanceIssue;

  /// No description provided for @userExperience.
  ///
  /// In en, this message translates to:
  /// **'User Experience'**
  String get userExperience;

  /// No description provided for @membershipService.
  ///
  /// In en, this message translates to:
  /// **'Membership Service'**
  String get membershipService;

  /// No description provided for @otherIssues.
  ///
  /// In en, this message translates to:
  /// **'Other Issues'**
  String get otherIssues;

  /// No description provided for @totalCost.
  ///
  /// In en, this message translates to:
  /// **'Total Cost'**
  String get totalCost;

  /// No description provided for @dailyAverage.
  ///
  /// In en, this message translates to:
  /// **'Daily Average'**
  String get dailyAverage;

  /// No description provided for @itemCount.
  ///
  /// In en, this message translates to:
  /// **'Item Count'**
  String get itemCount;

  /// No description provided for @costDistribution.
  ///
  /// In en, this message translates to:
  /// **'Cost Distribution by Category'**
  String get costDistribution;

  /// No description provided for @statusDistribution.
  ///
  /// In en, this message translates to:
  /// **'Status Distribution'**
  String get statusDistribution;

  /// No description provided for @dataRefreshed.
  ///
  /// In en, this message translates to:
  /// **'Data refreshed'**
  String get dataRefreshed;

  /// No description provided for @refreshData.
  ///
  /// In en, this message translates to:
  /// **'Refresh data'**
  String get refreshData;

  /// No description provided for @itemOverview.
  ///
  /// In en, this message translates to:
  /// **'Item Overview'**
  String get itemOverview;

  /// No description provided for @categoryDistribution.
  ///
  /// In en, this message translates to:
  /// **'Category Distribution'**
  String get categoryDistribution;

  /// No description provided for @highestDailyCostItems.
  ///
  /// In en, this message translates to:
  /// **'Highest Daily Cost Items'**
  String get highestDailyCostItems;

  /// No description provided for @totalItems.
  ///
  /// In en, this message translates to:
  /// **'Total Items'**
  String get totalItems;

  /// No description provided for @totalInvestment.
  ///
  /// In en, this message translates to:
  /// **'Total Investment'**
  String get totalInvestment;

  /// No description provided for @averageDailyCost.
  ///
  /// In en, this message translates to:
  /// **'Average Daily Cost'**
  String get averageDailyCost;

  /// No description provided for @daysUsed.
  ///
  /// In en, this message translates to:
  /// **'Days Used'**
  String get daysUsed;

  /// No description provided for @days.
  ///
  /// In en, this message translates to:
  /// **'days'**
  String get days;

  /// No description provided for @day.
  ///
  /// In en, this message translates to:
  /// **'day'**
  String get day;

  /// No description provided for @editItemTitle.
  ///
  /// In en, this message translates to:
  /// **'Edit Item'**
  String get editItemTitle;

  /// No description provided for @updateItem.
  ///
  /// In en, this message translates to:
  /// **'Update Item'**
  String get updateItem;

  /// No description provided for @pleaseEnterItemName.
  ///
  /// In en, this message translates to:
  /// **'Please enter item name'**
  String get pleaseEnterItemName;

  /// No description provided for @pleaseEnterPrice.
  ///
  /// In en, this message translates to:
  /// **'Please enter price'**
  String get pleaseEnterPrice;

  /// No description provided for @pleaseEnterValidPrice.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid price'**
  String get pleaseEnterValidPrice;

  /// No description provided for @pleaseSelectCategory.
  ///
  /// In en, this message translates to:
  /// **'Please select a category'**
  String get pleaseSelectCategory;

  /// No description provided for @pleaseSelectDate.
  ///
  /// In en, this message translates to:
  /// **'Please select purchase date'**
  String get pleaseSelectDate;

  /// No description provided for @selectImageSource.
  ///
  /// In en, this message translates to:
  /// **'Select Image Source'**
  String get selectImageSource;

  /// No description provided for @backupRestoreTitle.
  ///
  /// In en, this message translates to:
  /// **'Data Backup & Restore'**
  String get backupRestoreTitle;

  /// No description provided for @backupRestoreDescription.
  ///
  /// In en, this message translates to:
  /// **'You can backup your item data to the cloud, or restore your data from the cloud.'**
  String get backupRestoreDescription;

  /// No description provided for @backupToCloud.
  ///
  /// In en, this message translates to:
  /// **'Backup to Cloud'**
  String get backupToCloud;

  /// No description provided for @restoreFromCloud.
  ///
  /// In en, this message translates to:
  /// **'Restore from Cloud'**
  String get restoreFromCloud;

  /// No description provided for @backupCaution.
  ///
  /// In en, this message translates to:
  /// **'Backup and restore will overwrite existing data, please be cautious'**
  String get backupCaution;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @processingImage.
  ///
  /// In en, this message translates to:
  /// **'Processing image...'**
  String get processingImage;

  /// No description provided for @networkUnavailable.
  ///
  /// In en, this message translates to:
  /// **'Network connection unavailable, please check your network settings'**
  String get networkUnavailable;

  /// No description provided for @userInfoError.
  ///
  /// In en, this message translates to:
  /// **'Unable to get user information, please log in again'**
  String get userInfoError;

  /// No description provided for @noItemsToBackup.
  ///
  /// In en, this message translates to:
  /// **'No items need to be backed up'**
  String get noItemsToBackup;

  /// No description provided for @gettingCloudData.
  ///
  /// In en, this message translates to:
  /// **'Getting cloud data...'**
  String get gettingCloudData;

  /// No description provided for @getCloudDataFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to get cloud data: {error}'**
  String getCloudDataFailed(String error);

  /// No description provided for @deletingCloudItems.
  ///
  /// In en, this message translates to:
  /// **'Deleting unnecessary items from the cloud...'**
  String get deletingCloudItems;

  /// No description provided for @deleteCloudItemsFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to delete cloud items: {error}'**
  String deleteCloudItemsFailed(String error);

  /// No description provided for @uploadingLocalItems.
  ///
  /// In en, this message translates to:
  /// **'Uploading local items to the cloud ({current}/{total})...'**
  String uploadingLocalItems(int current, int total);

  /// No description provided for @partialUploadFailed.
  ///
  /// In en, this message translates to:
  /// **'Some items failed to upload, synchronized {success} items, failed {failed} items, deleted {deleted} items'**
  String partialUploadFailed(int success, int failed, int deleted);

  /// No description provided for @backupSuccess.
  ///
  /// In en, this message translates to:
  /// **'Data backup succeeded! Synchronized {added} items, deleted {deleted} items'**
  String backupSuccess(int added, int deleted);

  /// No description provided for @updatingItems.
  ///
  /// In en, this message translates to:
  /// **'Updating items ({current}/{total})...'**
  String updatingItems(int current, int total);

  /// No description provided for @backupSuccessWithUpdate.
  ///
  /// In en, this message translates to:
  /// **'Data backup succeeded! Synchronized {added} items, deleted {deleted} items, updated {updated} items'**
  String backupSuccessWithUpdate(int added, int deleted, int updated);

  /// No description provided for @backupError.
  ///
  /// In en, this message translates to:
  /// **'Error during backup: {error}'**
  String backupError(String error);

  /// No description provided for @restoreSuccess.
  ///
  /// In en, this message translates to:
  /// **'Successfully restored {count} items'**
  String restoreSuccess(int count);

  /// No description provided for @restoreFailed.
  ///
  /// In en, this message translates to:
  /// **'Restore failed: {error}'**
  String restoreFailed(String error);

  /// No description provided for @restoreError.
  ///
  /// In en, this message translates to:
  /// **'Error during restore: {error}'**
  String restoreError(String error);

  /// No description provided for @premiumFeature.
  ///
  /// In en, this message translates to:
  /// **'Premium Feature'**
  String get premiumFeature;

  /// No description provided for @upgradeToUsePremium.
  ///
  /// In en, this message translates to:
  /// **'Backup and restore features are only available to premium users. Upgrade to premium to use cloud backup and restore, making your data more secure.'**
  String get upgradeToUsePremium;

  /// No description provided for @upgradeToMember.
  ///
  /// In en, this message translates to:
  /// **'Upgrade Membership'**
  String get upgradeToMember;

  /// No description provided for @notNow.
  ///
  /// In en, this message translates to:
  /// **'Not Now'**
  String get notNow;

  /// No description provided for @itemLimitTitle.
  ///
  /// In en, this message translates to:
  /// **'Item limit reached'**
  String get itemLimitTitle;

  /// No description provided for @itemLimitDescription.
  ///
  /// In en, this message translates to:
  /// **'Free users can add up to 30 items. Please upgrade to premium to unlock unlimited item addition and cloud synchronization services.'**
  String get itemLimitDescription;

  /// No description provided for @lateTalk.
  ///
  /// In en, this message translates to:
  /// **'Maybe Later'**
  String get lateTalk;

  /// No description provided for @categoryLabel.
  ///
  /// In en, this message translates to:
  /// **'Category: {category}'**
  String categoryLabel(String category);

  /// No description provided for @basicInfo.
  ///
  /// In en, this message translates to:
  /// **'Basic Information'**
  String get basicInfo;

  /// No description provided for @usageDays.
  ///
  /// In en, this message translates to:
  /// **'{days} days'**
  String usageDays(int days);

  /// No description provided for @costAnalysis.
  ///
  /// In en, this message translates to:
  /// **'Cost Analysis'**
  String get costAnalysis;

  /// No description provided for @netCost.
  ///
  /// In en, this message translates to:
  /// **'Net Cost'**
  String get netCost;

  /// No description provided for @dailyCost.
  ///
  /// In en, this message translates to:
  /// **'Daily Cost'**
  String get dailyCost;

  /// No description provided for @weeklyCost.
  ///
  /// In en, this message translates to:
  /// **'{cost}/week'**
  String weeklyCost(String cost);

  /// No description provided for @monthlyCost.
  ///
  /// In en, this message translates to:
  /// **'{cost}/month'**
  String monthlyCost(String cost);

  /// No description provided for @yearlyCost.
  ///
  /// In en, this message translates to:
  /// **'{cost}/year'**
  String yearlyCost(String cost);

  /// No description provided for @costCap.
  ///
  /// In en, this message translates to:
  /// **'Note: Long-term cost is capped at the item\'s total value ({value})'**
  String costCap(String value);

  /// No description provided for @netCostCap.
  ///
  /// In en, this message translates to:
  /// **'Note: Long-term cost is capped at the item\'s net cost ({value})'**
  String netCostCap(String value);

  /// No description provided for @notes.
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get notes;

  /// No description provided for @itemStatus.
  ///
  /// In en, this message translates to:
  /// **'Item Status'**
  String get itemStatus;

  /// No description provided for @changeStatus.
  ///
  /// In en, this message translates to:
  /// **'Change Status'**
  String get changeStatus;

  /// No description provided for @statusActive.
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get statusActive;

  /// No description provided for @statusDamaged.
  ///
  /// In en, this message translates to:
  /// **'Damaged'**
  String get statusDamaged;

  /// No description provided for @statusInactive.
  ///
  /// In en, this message translates to:
  /// **'Inactive'**
  String get statusInactive;

  /// No description provided for @statusTransfer.
  ///
  /// In en, this message translates to:
  /// **'Transferred'**
  String get statusTransfer;

  /// No description provided for @statusDescription.
  ///
  /// In en, this message translates to:
  /// **'Status Description:\nActive - Item is normally counted in daily cost\nDamaged - Item is damaged and no longer counted in cost\nInactive - Item is no longer in use and not counted in cost\nTransferred - Item has been transferred to others and not counted in cost'**
  String get statusDescription;

  /// No description provided for @confirmDelete.
  ///
  /// In en, this message translates to:
  /// **'Confirm Delete'**
  String get confirmDelete;

  /// No description provided for @deleteConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete \"{name}\"? This action cannot be undone.'**
  String deleteConfirmation(String name);

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @transferItem.
  ///
  /// In en, this message translates to:
  /// **'Transfer Item'**
  String get transferItem;

  /// No description provided for @transferDescription.
  ///
  /// In en, this message translates to:
  /// **'Please enter the amount received when transferring out, this will help calculate the actual cost of the item.'**
  String get transferDescription;

  /// No description provided for @transferAmount.
  ///
  /// In en, this message translates to:
  /// **'Transfer Amount'**
  String get transferAmount;

  /// No description provided for @transferNote.
  ///
  /// In en, this message translates to:
  /// **'After transfer, the item will automatically be set to \"Inactive\" status and no longer counted in daily cost.'**
  String get transferNote;

  /// No description provided for @confirmTransfer.
  ///
  /// In en, this message translates to:
  /// **'Confirm Transfer'**
  String get confirmTransfer;

  /// No description provided for @pleaseEnterTransferAmount.
  ///
  /// In en, this message translates to:
  /// **'Please enter transfer amount'**
  String get pleaseEnterTransferAmount;

  /// No description provided for @pleaseEnterValidTransferAmount.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid transfer amount'**
  String get pleaseEnterValidTransferAmount;

  /// No description provided for @imageNotFound.
  ///
  /// In en, this message translates to:
  /// **'Image Not Found'**
  String get imageNotFound;

  /// No description provided for @cannotLoadImage.
  ///
  /// In en, this message translates to:
  /// **'Cannot Load Image'**
  String get cannotLoadImage;

  /// No description provided for @categoryElectronics.
  ///
  /// In en, this message translates to:
  /// **'Electronics'**
  String get categoryElectronics;

  /// No description provided for @categoryMobile.
  ///
  /// In en, this message translates to:
  /// **'Mobile'**
  String get categoryMobile;

  /// No description provided for @categoryComputer.
  ///
  /// In en, this message translates to:
  /// **'Computer'**
  String get categoryComputer;

  /// No description provided for @categoryHousehold.
  ///
  /// In en, this message translates to:
  /// **'Household'**
  String get categoryHousehold;

  /// No description provided for @categoryKitchen.
  ///
  /// In en, this message translates to:
  /// **'Kitchen'**
  String get categoryKitchen;

  /// No description provided for @categoryBooks.
  ///
  /// In en, this message translates to:
  /// **'Books'**
  String get categoryBooks;

  /// No description provided for @categoryClothing.
  ///
  /// In en, this message translates to:
  /// **'Clothing'**
  String get categoryClothing;

  /// No description provided for @categorySports.
  ///
  /// In en, this message translates to:
  /// **'Sports'**
  String get categorySports;

  /// No description provided for @categoryToys.
  ///
  /// In en, this message translates to:
  /// **'Toys'**
  String get categoryToys;

  /// No description provided for @categoryOffice.
  ///
  /// In en, this message translates to:
  /// **'Office'**
  String get categoryOffice;

  /// No description provided for @categoryOthers.
  ///
  /// In en, this message translates to:
  /// **'Others'**
  String get categoryOthers;

  /// No description provided for @imageProcessingError.
  ///
  /// In en, this message translates to:
  /// **'Error processing image'**
  String get imageProcessingError;

  /// No description provided for @imageSelectionError.
  ///
  /// In en, this message translates to:
  /// **'Error selecting image'**
  String get imageSelectionError;

  /// No description provided for @categoryTitle.
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get categoryTitle;

  /// No description provided for @week.
  ///
  /// In en, this message translates to:
  /// **'week'**
  String get week;

  /// No description provided for @month.
  ///
  /// In en, this message translates to:
  /// **'month'**
  String get month;

  /// No description provided for @year.
  ///
  /// In en, this message translates to:
  /// **'year'**
  String get year;

  /// No description provided for @costCapNote.
  ///
  /// In en, this message translates to:
  /// **'Note: Long-term cost is capped at the item\'s total value'**
  String get costCapNote;

  /// No description provided for @netCostCapNote.
  ///
  /// In en, this message translates to:
  /// **'Note: Long-term cost is capped at the item\'s net cost'**
  String get netCostCapNote;

  /// No description provided for @capped.
  ///
  /// In en, this message translates to:
  /// **'capped'**
  String get capped;

  /// No description provided for @yearsAgo.
  ///
  /// In en, this message translates to:
  /// **' years ago'**
  String get yearsAgo;

  /// No description provided for @monthsAgo.
  ///
  /// In en, this message translates to:
  /// **' months ago'**
  String get monthsAgo;

  /// No description provided for @daysAgo.
  ///
  /// In en, this message translates to:
  /// **' days ago'**
  String get daysAgo;

  /// No description provided for @hoursAgo.
  ///
  /// In en, this message translates to:
  /// **' hours ago'**
  String get hoursAgo;

  /// No description provided for @minutesAgo.
  ///
  /// In en, this message translates to:
  /// **' minutes ago'**
  String get minutesAgo;

  /// No description provided for @justNow.
  ///
  /// In en, this message translates to:
  /// **'Just now'**
  String get justNow;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @pleaseEnterEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter your email'**
  String get pleaseEnterEmail;

  /// No description provided for @pleaseEnterValidEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email address'**
  String get pleaseEnterValidEmail;

  /// No description provided for @pleaseEnterPassword.
  ///
  /// In en, this message translates to:
  /// **'Please enter your password'**
  String get pleaseEnterPassword;

  /// No description provided for @invalidEmailOrPassword.
  ///
  /// In en, this message translates to:
  /// **'Invalid email or password'**
  String get invalidEmailOrPassword;

  /// No description provided for @loginFailed.
  ///
  /// In en, this message translates to:
  /// **'Login failed: '**
  String get loginFailed;

  /// No description provided for @offlineLoginWarning.
  ///
  /// In en, this message translates to:
  /// **'Network is unavailable, data won\'t be synced after login'**
  String get offlineLoginWarning;

  /// No description provided for @retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// No description provided for @noAccount.
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get noAccount;

  /// No description provided for @registerNow.
  ///
  /// In en, this message translates to:
  /// **'Register now'**
  String get registerNow;

  /// No description provided for @forgotPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot password?'**
  String get forgotPassword;

  /// No description provided for @username.
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get username;

  /// No description provided for @pleaseEnterUsername.
  ///
  /// In en, this message translates to:
  /// **'Please enter your username'**
  String get pleaseEnterUsername;

  /// No description provided for @confirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// No description provided for @pleaseConfirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Please confirm your password'**
  String get pleaseConfirmPassword;

  /// No description provided for @passwordsDoNotMatch.
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get passwordsDoNotMatch;

  /// No description provided for @verificationCode.
  ///
  /// In en, this message translates to:
  /// **'Verification Code'**
  String get verificationCode;

  /// No description provided for @pleaseEnterVerificationCode.
  ///
  /// In en, this message translates to:
  /// **'Please enter verification code'**
  String get pleaseEnterVerificationCode;

  /// No description provided for @getVerificationCode.
  ///
  /// In en, this message translates to:
  /// **'Get Code'**
  String get getVerificationCode;

  /// No description provided for @retryAfterSeconds.
  ///
  /// In en, this message translates to:
  /// **'Retry in {seconds}s'**
  String retryAfterSeconds(int seconds);

  /// No description provided for @verificationCodeSent.
  ///
  /// In en, this message translates to:
  /// **'Verification code has been sent to your email'**
  String get verificationCodeSent;

  /// No description provided for @codeSendingFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to send verification code, email may already be registered'**
  String get codeSendingFailed;

  /// No description provided for @registrationSuccess.
  ///
  /// In en, this message translates to:
  /// **'Registration successful, please login'**
  String get registrationSuccess;

  /// No description provided for @registrationFailed.
  ///
  /// In en, this message translates to:
  /// **'Registration failed, verification code may be invalid or expired'**
  String get registrationFailed;

  /// No description provided for @passwordMinLength.
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 6 characters'**
  String get passwordMinLength;

  /// No description provided for @returnToLogin.
  ///
  /// In en, this message translates to:
  /// **'Back to login'**
  String get returnToLogin;

  /// No description provided for @alreadyHaveAccount.
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get alreadyHaveAccount;

  /// No description provided for @resetPassword.
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get resetPassword;

  /// No description provided for @resetPasswordSuccess.
  ///
  /// In en, this message translates to:
  /// **'Password reset successful, please login with your new password'**
  String get resetPasswordSuccess;

  /// No description provided for @resetPasswordFailed.
  ///
  /// In en, this message translates to:
  /// **'Password reset failed, verification code may be invalid or expired'**
  String get resetPasswordFailed;

  /// No description provided for @newPassword.
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get newPassword;

  /// No description provided for @confirmNewPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm New Password'**
  String get confirmNewPassword;

  /// No description provided for @pleaseEnterNewPassword.
  ///
  /// In en, this message translates to:
  /// **'Please enter new password'**
  String get pleaseEnterNewPassword;

  /// No description provided for @pleaseConfirmNewPassword.
  ///
  /// In en, this message translates to:
  /// **'Please confirm new password'**
  String get pleaseConfirmNewPassword;

  /// No description provided for @rememberPassword.
  ///
  /// In en, this message translates to:
  /// **'Remember your password?'**
  String get rememberPassword;

  /// No description provided for @resetPasswordTitle.
  ///
  /// In en, this message translates to:
  /// **'Reset Your Password'**
  String get resetPasswordTitle;

  /// No description provided for @appSlogan.
  ///
  /// In en, this message translates to:
  /// **'Smart Cost Tracking for Your Items'**
  String get appSlogan;

  /// No description provided for @upgradeToUsePremiumDesc.
  ///
  /// In en, this message translates to:
  /// **'Backup and restore features are only available to premium users. Upgrade to premium to use cloud backup and restore for more secure data.'**
  String get upgradeToUsePremiumDesc;

  /// No description provided for @quotaRefreshed.
  ///
  /// In en, this message translates to:
  /// **'Available item quota refreshed'**
  String get quotaRefreshed;

  /// No description provided for @loggedOut.
  ///
  /// In en, this message translates to:
  /// **'Logged out successfully'**
  String get loggedOut;

  /// No description provided for @overview.
  ///
  /// In en, this message translates to:
  /// **'Overview'**
  String get overview;

  /// No description provided for @trends.
  ///
  /// In en, this message translates to:
  /// **'Trends'**
  String get trends;

  /// No description provided for @addItemsToSeeStats.
  ///
  /// In en, this message translates to:
  /// **'Add items to see statistics'**
  String get addItemsToSeeStats;

  /// No description provided for @addItem.
  ///
  /// In en, this message translates to:
  /// **'Add Item'**
  String get addItem;

  /// No description provided for @itemUsageTime.
  ///
  /// In en, this message translates to:
  /// **'Item Usage Time'**
  String get itemUsageTime;

  /// No description provided for @categoryAnalysis.
  ///
  /// In en, this message translates to:
  /// **'Category Analysis'**
  String get categoryAnalysis;

  /// No description provided for @items.
  ///
  /// In en, this message translates to:
  /// **'Items'**
  String get items;

  /// No description provided for @costTrends.
  ///
  /// In en, this message translates to:
  /// **'Cost Trends'**
  String get costTrends;

  /// No description provided for @monthlyExpenditure.
  ///
  /// In en, this message translates to:
  /// **'Monthly Expenditure'**
  String get monthlyExpenditure;

  /// No description provided for @costOptimization.
  ///
  /// In en, this message translates to:
  /// **'Cost Optimization'**
  String get costOptimization;

  /// No description provided for @reduceShoppingTip.
  ///
  /// In en, this message translates to:
  /// **'Reduce shopping frequency to save about 15% of expenses'**
  String get reduceShoppingTip;

  /// No description provided for @electronicsTip.
  ///
  /// In en, this message translates to:
  /// **'Extend electronics usage time to reduce 25% of cost'**
  String get electronicsTip;

  /// No description provided for @investmentTip.
  ///
  /// In en, this message translates to:
  /// **'Consider investing in high-value, long-cycle items'**
  String get investmentTip;

  /// No description provided for @membershipBenefits.
  ///
  /// In en, this message translates to:
  /// **'Membership Benefits'**
  String get membershipBenefits;

  /// No description provided for @advancedAnalytics.
  ///
  /// In en, this message translates to:
  /// **'Advanced Data Analytics'**
  String get advancedAnalytics;

  /// No description provided for @advancedAnalyticsDesc.
  ///
  /// In en, this message translates to:
  /// **'Unlock detailed spending trend charts and smart consumption recommendations'**
  String get advancedAnalyticsDesc;

  /// No description provided for @cloudBackup.
  ///
  /// In en, this message translates to:
  /// **'Cloud Backup'**
  String get cloudBackup;

  /// No description provided for @cloudBackupDesc.
  ///
  /// In en, this message translates to:
  /// **'Automatically protect your important data, never lose it'**
  String get cloudBackupDesc;

  /// No description provided for @multiDeviceSync.
  ///
  /// In en, this message translates to:
  /// **'Multi-device Sync'**
  String get multiDeviceSync;

  /// No description provided for @multiDeviceSyncDesc.
  ///
  /// In en, this message translates to:
  /// **'Seamlessly switch between phone, tablet, and computer'**
  String get multiDeviceSyncDesc;

  /// No description provided for @unlimitedItems.
  ///
  /// In en, this message translates to:
  /// **'Unlimited Items'**
  String get unlimitedItems;

  /// No description provided for @unlimitedItemsDesc.
  ///
  /// In en, this message translates to:
  /// **'Break through the 30-item limit, add all your valuable items'**
  String get unlimitedItemsDesc;

  /// No description provided for @customThemes.
  ///
  /// In en, this message translates to:
  /// **'Custom Themes'**
  String get customThemes;

  /// No description provided for @customThemesDesc.
  ///
  /// In en, this message translates to:
  /// **'Personalized interface and more visual options'**
  String get customThemesDesc;

  /// No description provided for @adFreeExperience.
  ///
  /// In en, this message translates to:
  /// **'Ad-Free Experience'**
  String get adFreeExperience;

  /// No description provided for @adFreeExperienceDesc.
  ///
  /// In en, this message translates to:
  /// **'Focus on using the app without any distractions'**
  String get adFreeExperienceDesc;

  /// No description provided for @chooseMembershipPlan.
  ///
  /// In en, this message translates to:
  /// **'Choose Your Membership Plan'**
  String get chooseMembershipPlan;

  /// No description provided for @lifetimeMembership.
  ///
  /// In en, this message translates to:
  /// **'Lifetime'**
  String get lifetimeMembership;

  /// No description provided for @lifetimeMembershipDesc.
  ///
  /// In en, this message translates to:
  /// **'Pay once, enjoy forever - All premium features permanently unlocked, most economical for long-term use'**
  String get lifetimeMembershipDesc;

  /// No description provided for @annualMembership.
  ///
  /// In en, this message translates to:
  /// **'Annual'**
  String get annualMembership;

  /// No description provided for @annualMembershipDesc.
  ///
  /// In en, this message translates to:
  /// **'Annual plan saves more - Unlock all premium features for a year of worry-free experience'**
  String get annualMembershipDesc;

  /// No description provided for @monthlyMembership.
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get monthlyMembership;

  /// No description provided for @monthlyMembershipDesc.
  ///
  /// In en, this message translates to:
  /// **'Flexible subscription - Start or cancel anytime, easy access to all premium features'**
  String get monthlyMembershipDesc;

  /// No description provided for @bestValue.
  ///
  /// In en, this message translates to:
  /// **'Best Value'**
  String get bestValue;

  /// No description provided for @save50.
  ///
  /// In en, this message translates to:
  /// **'Save 50%'**
  String get save50;

  /// No description provided for @buyNow.
  ///
  /// In en, this message translates to:
  /// **'Buy Now'**
  String get buyNow;

  /// No description provided for @processing.
  ///
  /// In en, this message translates to:
  /// **'Processing...'**
  String get processing;

  /// No description provided for @monthAbbr1.
  ///
  /// In en, this message translates to:
  /// **'Jan'**
  String get monthAbbr1;

  /// No description provided for @monthAbbr2.
  ///
  /// In en, this message translates to:
  /// **'Feb'**
  String get monthAbbr2;

  /// No description provided for @monthAbbr3.
  ///
  /// In en, this message translates to:
  /// **'Mar'**
  String get monthAbbr3;

  /// No description provided for @monthAbbr4.
  ///
  /// In en, this message translates to:
  /// **'Apr'**
  String get monthAbbr4;

  /// No description provided for @monthAbbr5.
  ///
  /// In en, this message translates to:
  /// **'May'**
  String get monthAbbr5;

  /// No description provided for @monthAbbr6.
  ///
  /// In en, this message translates to:
  /// **'Jun'**
  String get monthAbbr6;

  /// No description provided for @monthAbbr7.
  ///
  /// In en, this message translates to:
  /// **'Jul'**
  String get monthAbbr7;

  /// No description provided for @monthAbbr8.
  ///
  /// In en, this message translates to:
  /// **'Aug'**
  String get monthAbbr8;

  /// No description provided for @monthAbbr9.
  ///
  /// In en, this message translates to:
  /// **'Sep'**
  String get monthAbbr9;

  /// No description provided for @monthAbbr10.
  ///
  /// In en, this message translates to:
  /// **'Oct'**
  String get monthAbbr10;

  /// No description provided for @monthAbbr11.
  ///
  /// In en, this message translates to:
  /// **'Nov'**
  String get monthAbbr11;

  /// No description provided for @monthAbbr12.
  ///
  /// In en, this message translates to:
  /// **'Dec'**
  String get monthAbbr12;

  /// No description provided for @restorePurchases.
  ///
  /// In en, this message translates to:
  /// **'Restore Purchases'**
  String get restorePurchases;

  /// No description provided for @restoringPurchases.
  ///
  /// In en, this message translates to:
  /// **'Restoring purchases...'**
  String get restoringPurchases;

  /// No description provided for @purchaseRestoreSuccess.
  ///
  /// In en, this message translates to:
  /// **'Purchases restored successfully'**
  String get purchaseRestoreSuccess;

  /// No description provided for @purchaseRestoreFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to restore purchases'**
  String get purchaseRestoreFailed;

  /// No description provided for @transferDateTitle.
  ///
  /// In en, this message translates to:
  /// **'Transfer Date'**
  String get transferDateTitle;

  /// No description provided for @damageDate.
  ///
  /// In en, this message translates to:
  /// **'Damage Date'**
  String get damageDate;

  /// No description provided for @inactiveDate.
  ///
  /// In en, this message translates to:
  /// **'Inactive Date'**
  String get inactiveDate;

  /// No description provided for @statusDateDescription.
  ///
  /// In en, this message translates to:
  /// **'Please select the date when the status changed. This will be used to calculate the actual usage cost of the item.'**
  String get statusDateDescription;

  /// No description provided for @selectDate.
  ///
  /// In en, this message translates to:
  /// **'Select Date'**
  String get selectDate;

  /// No description provided for @useCurrentDate.
  ///
  /// In en, this message translates to:
  /// **'Use Current Date'**
  String get useCurrentDate;

  /// No description provided for @itemStatusActiveSuccess.
  ///
  /// In en, this message translates to:
  /// **'Item has been set to active status'**
  String get itemStatusActiveSuccess;

  /// No description provided for @itemStatusTransferSuccess.
  ///
  /// In en, this message translates to:
  /// **'Item has been transferred, transfer amount: {amount}, end date: {date}'**
  String itemStatusTransferSuccess(String amount, String date);

  /// No description provided for @itemStatusChangeSuccess.
  ///
  /// In en, this message translates to:
  /// **'Item status has been updated, end date: {date}'**
  String itemStatusChangeSuccess(String date);

  /// No description provided for @splashInitializing.
  ///
  /// In en, this message translates to:
  /// **'Initializing...'**
  String get splashInitializing;

  /// No description provided for @splashLoading.
  ///
  /// In en, this message translates to:
  /// **'Starting up...'**
  String get splashLoading;

  /// No description provided for @splashWelcome.
  ///
  /// In en, this message translates to:
  /// **'Welcome to CostTrack'**
  String get splashWelcome;

  /// No description provided for @splashInitializationComplete.
  ///
  /// In en, this message translates to:
  /// **'Ready to go'**
  String get splashInitializationComplete;

  /// No description provided for @splashInitializationFailed.
  ///
  /// In en, this message translates to:
  /// **'Startup failed: {error}'**
  String splashInitializationFailed(String error);
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'zh': return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
