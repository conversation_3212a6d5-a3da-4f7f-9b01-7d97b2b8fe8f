import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:calculate_cost_flutter/l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import 'screens/add_item_screen.dart';
import 'screens/edit_item_screen.dart';
import 'screens/item_details_screen.dart';
import 'screens/statistics_screen.dart';
import 'screens/login_screen.dart';
import 'screens/register_screen.dart';
import 'screens/membership_screen.dart';
import 'screens/feedback_screen.dart';
import 'screens/reset_password_screen.dart';
import 'screens/splash_screen.dart';
import 'screens/home_screen.dart';
import 'models/item.dart';
import 'utils/constants.dart';
import 'services/api_auth_service.dart';
import 'services/base_auth_service.dart';
import 'services/purchase_service.dart';
import 'services/payment_service.dart';
import 'providers/app_settings_provider.dart';
import 'providers/base_item_provider.dart';
import 'providers/item_provider.dart';
import 'providers/api_item_provider.dart';

// 是否使用API服务
const bool useApi = false;

void main() async {
  // 确保Flutter初始化完成
  WidgetsFlutterBinding.ensureInitialized();

  // 只进行必要的同步初始化，其他异步操作移到启动屏幕中进行
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // 创建全局的认证服务Provider - 使用抽象类型
        ChangeNotifierProvider<BaseAuthService>(
          create: (_) => ApiAuthService(),
        ),
        // 创建全局的物品Provider - 使用抽象类型
        ChangeNotifierProvider<BaseItemProvider>(
          create: (_) => useApi ? ApiItemProvider() : ItemProvider(),
        ),
        // 创建全局的设置Provider
        ChangeNotifierProvider<AppSettingsProvider>(
          create: (_) => AppSettingsProvider(),
        ),
        // 创建全局的购买服务Provider
        ChangeNotifierProvider<PurchaseService>(
          create: (_) => PurchaseService(),
        ),
        // 创建全局的支付服务Provider
        Provider<PaymentService>(create: (_) => PaymentService()),
      ],
      child: Consumer<AppSettingsProvider>(
        builder: (context, settings, child) {
          return _buildMaterialApp(context, settings);
        },
      ),
    );
  }

  // 转换AppThemeMode到ThemeMode
  ThemeMode _getThemeMode(AppThemeMode appThemeMode) {
    switch (appThemeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }

  Widget _buildMaterialApp(BuildContext context, AppSettingsProvider settings) {
    // 创建主题
    final lightTheme = ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFFFFC107), // 明亮的黄色作为种子颜色
        brightness: Brightness.light,
        primary: const Color(0xFFFFC107),
        secondary: const Color(0xFF00BCD4),
        tertiary: const Color(0xFFE91E63),
        // 自定义其他颜色
        onPrimary: Colors.black,
        primaryContainer: const Color(0xFFFFE082),
        onPrimaryContainer: const Color(0xFF323232),
      ),
      useMaterial3: true,
      fontFamily: 'Roboto',
      // 卡片样式
      cardTheme: CardTheme(
        elevation: 3,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        clipBehavior: Clip.antiAlias,
      ),
      // 按钮样式
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
      // 图标主题
      iconTheme: const IconThemeData(size: 24, color: Color(0xFFFFC107)),
      // AppBar主题
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFFFFC107),
        foregroundColor: Colors.black87,
        elevation: 0,
        centerTitle: true,
      ),
    );

    final darkTheme = ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFFFFC107),
        brightness: Brightness.dark,
        primary: const Color(0xFFFFC107),
        secondary: const Color(0xFF00BCD4),
        tertiary: const Color(0xFFE91E63),
        surface: const Color(0xFF1E1E1E),
        onSurface: Colors.white,
      ),
      useMaterial3: true,
      fontFamily: 'Roboto',
      // 卡片样式
      cardTheme: CardTheme(
        elevation: 3,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        clipBehavior: Clip.antiAlias,
      ),
      // 按钮样式
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
      // 图标主题
      iconTheme: const IconThemeData(size: 24, color: Color(0xFFFFC107)),
      // AppBar主题
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF1E1E1E),
        foregroundColor: Color(0xFFFFC107),
        elevation: 0,
        centerTitle: true,
      ),
    );

    return MaterialApp(
      title: 'CostTrack',
      theme: lightTheme,
      darkTheme: darkTheme,
      themeMode: _getThemeMode(settings.themeMode), // 使用设置中的主题
      locale: settings.locale, // 使用设置中的语言
      // 国际化支持
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en'), // 英文
        Locale('zh'), // 中文
      ],

      // 首先显示启动页面
      home: const SplashScreen(),
      routes: {
        '/home': (context) => const HomeScreen(),
        AppRoutes.addItem: (context) => const AddItemScreen(),
        AppRoutes.statistics: (context) => const StatisticsScreen(),
        AppRoutes.login: (context) => const LoginScreen(),
        AppRoutes.register: (context) => const RegisterScreen(),
        AppRoutes.membership: (context) => const MembershipScreen(),
        AppRoutes.feedback: (context) => const FeedbackScreen(),
        AppRoutes.resetPassword: (context) => const ResetPasswordScreen(),
      },
      onGenerateRoute: (settings) {
        if (settings.name == AppRoutes.itemDetails) {
          final Item item = settings.arguments as Item;
          return MaterialPageRoute(
            builder: (context) => ItemDetailsScreen(item: item),
          );
        } else if (settings.name == AppRoutes.editItem) {
          final Item item = settings.arguments as Item;
          return MaterialPageRoute(
            builder: (context) => EditItemScreen(item: item),
          );
        }
        return null;
      },
    );
  }
}
