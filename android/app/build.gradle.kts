plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

import java.util.Properties

// Function to read properties from a file
fun loadProperties(filePath: String): Properties {
    val properties = Properties()
    val propertiesFile = File(filePath)
    if (propertiesFile.exists()) {
        propertiesFile.inputStream().use {
            properties.load(it)
        }
    }
    return properties
}

// Read Flutter version details from local.properties
val localProperties = Properties()
val localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.inputStream().use { stream ->
        localProperties.load(stream)
    }
}

val flutterVersionCode = localProperties.getProperty("flutter.versionCode")?.toInt() ?: 1
val flutterVersionName = localProperties.getProperty("flutter.versionName") ?: "1.0.0"

android {
    namespace = "com.sato.calculate_cost_flutter"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.sato.calculate_cost_flutter"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 21 // 设置最低支持的Android版本
        targetSdk = 34 // 确保目标SDK版本为34 (Android 14)
        versionCode = flutterVersionCode // 从 pubspec.yaml 读取
        versionName = flutterVersionName // 从 pubspec.yaml 读取
    }

    signingConfigs {
        create("release") {
            storeFile = file("costtrack-release-key.jks")
            storePassword = project.findProperty("RELEASE_STORE_PASSWORD") as String? ?: ""
            keyAlias = "costtrack"
            keyPassword = project.findProperty("RELEASE_KEY_PASSWORD") as String? ?: ""
        }
    }

    buildTypes {
        release {
            // 启用代码压缩、优化和混淆
            isMinifyEnabled = true
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
            
            // 暂时注释掉release签名配置，使用debug签名进行测试
            signingConfig = signingConfigs.getByName("release")
            
            // 临时使用debug签名配置
            //// // // // // // // // // // // // // // // // // // // // // // // // // signingConfig = signingConfigs.getByName("debug")
        }
    }
}

// 添加依赖项
dependencies {
    // 移除旧的 Play Core 库
    // implementation("com.google.android.play:core:1.10.3")
    // implementation("com.google.android.play:core-ktx:1.8.1")
    
    // 添加新的 Play In-App Update 库
    implementation("com.google.android.play:app-update:2.1.0")
    implementation("com.google.android.play:app-update-ktx:2.1.0")
    
    // 如果需要使用动态功能模块，添加 Play Feature Delivery 库
    implementation("com.google.android.play:feature-delivery:2.1.0")
    implementation("com.google.android.play:feature-delivery-ktx:2.1.0")
}

flutter {
    source = "../.."
}
